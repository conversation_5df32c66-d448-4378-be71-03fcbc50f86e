import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "../ui/tabs";
import { UseCaseOverviewDashboard } from "./UseCaseOverviewDashboard";
import { CalculationMethodology } from "./CalculationMethodology";
import {
  generateUseCaseRecommendations,
  findBestModelsForUseCases,
  STANDARD_USE_CASES,
} from "@/services/model-recommendations";
import type { ModelCard } from "@/types/model-cards";
import type { UseCaseRecommendations } from "@/types/model-recommendations";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Button } from "../ui/button";
import { Star, TrendingUp, Target, BarChart3, CheckCircle } from "lucide-react";

interface RecommendationsPageProps {
  modelCards: ModelCard[];
  onModelSelect?: (modelId: string) => void;
}

interface TopModelCardProps {
  modelCard: ModelCard;
  averageScore: number;
  useCaseScores: { [useCaseId: string]: number };
  onModelSelect?: (modelId: string) => void;
}

const TopModelCard: React.FC<TopModelCardProps> = ({
  modelCard,
  averageScore,
  useCaseScores,
  onModelSelect,
}) => {
  const getScoreColor = (score: number) => {
    if (score >= 80)
      return "text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/30";
    if (score >= 65)
      return "text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/30";
    if (score >= 50)
      return "text-yellow-600 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-900/30";
    return "text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/30";
  };

  const topUseCases = Object.entries(useCaseScores)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 3)
    .map(([useCaseId, score]) => ({
      name:
        STANDARD_USE_CASES.find((uc) => uc.id === useCaseId)?.name || useCaseId,
      score,
    }));

  return (
    <Card
      className="cursor-pointer hover:shadow-md transition-shadow"
      onClick={() => onModelSelect?.(modelCard.basicInfo.modelId)}
    >
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">
              {modelCard.basicInfo.displayName}
            </CardTitle>
            <CardDescription>{modelCard.basicInfo.provider}</CardDescription>
          </div>
          <div
            className={`px-3 py-1 rounded-lg font-bold ${getScoreColor(
              averageScore
            )}`}
          >
            {averageScore.toFixed(1)}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-muted-foreground">
            Top Use Cases:
          </h4>
          {topUseCases.map((uc, _index) => (
            <div
              key={`usecase-${uc.name}`}
              className="flex justify-between items-center text-sm"
            >
              <span className="text-muted-foreground">{uc.name}</span>
              <span className={`font-medium ${getScoreColor(uc.score)}`}>
                {uc.score.toFixed(1)}
              </span>
            </div>
          ))}
        </div>
        <div className="mt-4 pt-3 border-t">
          <div className="text-xs text-muted-foreground">
            ${modelCard.pricing.inputCostPer1MTokens.toFixed(2)}/1M Input • $
            {modelCard.pricing.outputCostPer1MTokens.toFixed(2)}/1M Output
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

interface UseCaseDetailCardProps {
  useCaseRecommendations: UseCaseRecommendations;
  onModelSelect?: (modelId: string) => void;
}

const UseCaseDetailCard: React.FC<UseCaseDetailCardProps> = ({
  useCaseRecommendations,
  onModelSelect,
}) => {
  const { useCase, recommendedModels, alternativeModels } =
    useCaseRecommendations;

  const allModels = [...recommendedModels, ...alternativeModels].slice(0, 10);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="w-5 h-5 text-blue-600" />
          {useCase.name}
        </CardTitle>
        <CardDescription>{useCase.description}</CardDescription>

        {/* Use Case Details: Benchmarks and Capabilities */}
        <div className="mt-4 space-y-3">
          <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
              📊 Verwendete Benchmarks:
            </h4>
            <div className="flex flex-wrap gap-1">
              {useCase.optimalBenchmarks.map((benchmark, _index) => (
                <span
                  key={`benchmark-${benchmark}`}
                  className="inline-block px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-800/50 dark:text-blue-200 text-xs rounded-md"
                >
                  {benchmark}
                </span>
              ))}
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900/30 p-3 rounded-lg">
            <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-2">
              ⚙️ Benötigte Capabilities:
            </h4>
            <div className="flex flex-wrap gap-1">
              {useCase.requiredCapabilities.map((capability, _index) => (
                <span
                  key={`capability-${capability}`}
                  className="inline-block px-2 py-1 bg-green-100 text-green-800 dark:bg-green-800/50 dark:text-green-200 text-xs rounded-md"
                >
                  {capability}
                </span>
              ))}
            </div>
          </div>

          <div className="bg-muted p-2 rounded-lg">
            <div className="flex items-center justify-between text-xs">
              <span className="text-muted-foreground">
                Kategorie:{" "}
                <span className="font-medium">{useCase.category}</span>
              </span>
              <span
                className={`px-2 py-1 rounded-md font-medium ${
                  useCase.priority === "high"
                    ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                    : useCase.priority === "medium"
                    ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                {useCase.priority === "high"
                  ? "Hoch"
                  : useCase.priority === "medium"
                  ? "Mittel"
                  : "Niedrig"}{" "}
                Priorität
              </span>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-muted-foreground border-b pb-2">
            Top Empfehlungen:
          </h4>
          {allModels.map((rec, index) => (
            <div
              key={rec.modelId}
              className="flex justify-between items-center p-2 border rounded cursor-pointer hover:bg-accent"
              onClick={() => onModelSelect?.(rec.modelId)}
            >
              <div className="flex items-center gap-2">
                <span className="text-lg">{index + 1}.</span>
                <div>
                  <div className="font-medium">{rec.modelId}</div>
                  <div className="text-xs text-muted-foreground">
                    {rec.suitability}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-blue-600 dark:text-blue-400">
                  {rec.score}
                </div>
                <div className="text-xs text-muted-foreground">
                  {rec.costEffectiveness === "high"
                    ? "Kostengünstig"
                    : rec.costEffectiveness === "medium"
                    ? "Standard"
                    : "Teuer"}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export const RecommendationsPage: React.FC<RecommendationsPageProps> = ({
  modelCards,
  onModelSelect,
}) => {
  // Generate comprehensive recommendations
  const allUseCaseRecommendations = React.useMemo(() => {
    return STANDARD_USE_CASES.map((useCase) =>
      generateUseCaseRecommendations(modelCards, useCase)
    );
  }, [modelCards]);

  // Find best overall models across multiple use cases
  const bestOverallModels = React.useMemo(() => {
    const allUseCaseIds = STANDARD_USE_CASES.map((uc) => uc.id);
    return findBestModelsForUseCases(modelCards, allUseCaseIds).slice(0, 6);
  }, [modelCards]);

  // Find best models for high-priority use cases
  const highPriorityUseCases = STANDARD_USE_CASES.filter(
    (uc) => uc.priority === "high"
  ).map((uc) => uc.id);
  const bestHighPriorityModels = React.useMemo(() => {
    return findBestModelsForUseCases(modelCards, highPriorityUseCases).slice(
      0,
      6
    );
  }, [modelCards, highPriorityUseCases]);

  // Get statistics
  const stats = React.useMemo(() => {
    const totalRecommendations = allUseCaseRecommendations.reduce(
      (sum, rec) => sum + rec.recommendedModels.length,
      0
    );

    const excellentRecommendations = allUseCaseRecommendations.reduce(
      (sum, rec) =>
        sum +
        rec.recommendedModels.filter((r) => r.suitability === "excellent")
          .length,
      0
    );

    return {
      totalUseCases: STANDARD_USE_CASES.length,
      totalModels: modelCards.length,
      totalRecommendations,
      excellentRecommendations,
      averageRecommendationsPerUseCase:
        totalRecommendations / STANDARD_USE_CASES.length,
    };
  }, [allUseCaseRecommendations, modelCards]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
        <div className="flex justify-between items-start mb-4">
          <h1 className="text-3xl font-bold text-foreground">
            Model-Empfehlungen für Unternehmen
          </h1>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open("/quality-check/", "_blank")}
              className="flex items-center gap-2"
            >
              <CheckCircle className="w-4 h-4" />
              Quality-Check
            </Button>
            <CalculationMethodology />
          </div>
        </div>
        <p className="text-muted-foreground mb-6">
          Intelligente Empfehlungen für die Auswahl des optimalen LLM-Modells
          basierend auf Standard-Anwendungsfällen in Unternehmen. Berücksichtigt
          Performance, Kosten, Capabilities und weitere Faktoren.
        </p>

        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="bg-card rounded-lg p-4 border">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {stats.totalUseCases}
            </div>
            <div className="text-sm text-muted-foreground">Use Cases</div>
          </div>
          <div className="bg-card rounded-lg p-4 border">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {stats.totalModels}
            </div>
            <div className="text-sm text-muted-foreground">Modelle</div>
          </div>
          <div className="bg-card rounded-lg p-4 border">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {stats.totalRecommendations}
            </div>
            <div className="text-sm text-muted-foreground">Empfehlungen</div>
          </div>
          <div className="bg-card rounded-lg p-4 border">
            <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
              {stats.excellentRecommendations}
            </div>
            <div className="text-sm text-muted-foreground">Exzellent</div>
          </div>
          <div className="bg-card rounded-lg p-4 border">
            <div className="text-2xl font-bold text-teal-600 dark:text-teal-400">
              {stats.averageRecommendationsPerUseCase.toFixed(1)}
            </div>
            <div className="text-sm text-muted-foreground">⌀ pro Use Case</div>
          </div>
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Übersicht</TabsTrigger>
          <TabsTrigger value="top-models">Top Modelle</TabsTrigger>
          <TabsTrigger value="high-priority">Kritische Use Cases</TabsTrigger>
          <TabsTrigger value="use-cases">Alle Use Cases</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <UseCaseOverviewDashboard
            modelCards={modelCards}
            onModelSelect={onModelSelect}
          />
        </TabsContent>

        <TabsContent value="top-models" className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold text-foreground mb-4 flex items-center gap-2">
              <Star className="w-6 h-6 text-yellow-500 dark:text-yellow-400" />
              Beste Gesamtmodelle
            </h2>
            <p className="text-muted-foreground mb-6">
              Modelle mit der besten durchschnittlichen Performance über alle
              Use Cases hinweg.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {bestOverallModels.map((result) => {
                const modelCard = modelCards.find(
                  (mc) => mc.basicInfo.modelId === result.modelId
                );
                return modelCard ? (
                  <TopModelCard
                    key={result.modelId}
                    modelCard={modelCard}
                    averageScore={result.averageScore}
                    useCaseScores={result.useCaseScores}
                    onModelSelect={onModelSelect}
                  />
                ) : null;
              })}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="high-priority" className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold text-foreground mb-4 flex items-center gap-2">
              <TrendingUp className="w-6 h-6 text-red-500 dark:text-red-400" />
              Kritische Use Cases
            </h2>
            <p className="text-muted-foreground mb-6">
              Empfehlungen für geschäftskritische Anwendungsfälle mit hoher
              Priorität.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {bestHighPriorityModels.map((result) => {
                const modelCard = modelCards.find(
                  (mc) => mc.basicInfo.modelId === result.modelId
                );
                return modelCard ? (
                  <TopModelCard
                    key={result.modelId}
                    modelCard={modelCard}
                    averageScore={result.averageScore}
                    useCaseScores={result.useCaseScores}
                    onModelSelect={onModelSelect}
                  />
                ) : null;
              })}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {allUseCaseRecommendations
                .filter((rec) => rec.useCase.priority === "high")
                .map((rec) => (
                  <UseCaseDetailCard
                    key={rec.useCase.id}
                    useCaseRecommendations={rec}
                    onModelSelect={onModelSelect}
                  />
                ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="use-cases" className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold text-foreground mb-4 flex items-center gap-2">
              <BarChart3 className="w-6 h-6 text-blue-500 dark:text-blue-400" />
              Alle Use Cases
            </h2>
            <p className="text-muted-foreground mb-6">
              Detaillierte Empfehlungen für jeden Standard-Anwendungsfall.
            </p>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {allUseCaseRecommendations.map((rec) => (
                <UseCaseDetailCard
                  key={rec.useCase.id}
                  useCaseRecommendations={rec}
                  onModelSelect={onModelSelect}
                />
              ))}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
