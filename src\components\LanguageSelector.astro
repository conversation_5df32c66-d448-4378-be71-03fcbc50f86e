---
import { changeLanguage } from "../utils/i18n";
---

<div class="language-selector">
  <select id="language-select" class="language-select">
    <option value="de">De<PERSON>ch</option>
    <option value="en">English</option>
    <option value="pl"><PERSON><PERSON></option>
  </select>
</div>

<style>
  .language-selector {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
  }

  .language-select {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    background-color: white;
    font-size: 0.875rem;
    color: #1e293b;
    cursor: pointer;
    transition: border-color 0.2s;
  }

  .language-select:hover {
    border-color: #94a3b8;
  }

  .language-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
</style>

<script>
  function getCookie(name: string): string | null {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(";").shift() || null;
    return null;
  }

  document.addEventListener("DOMContentLoaded", () => {
    const languageSelect = document.getElementById(
      "language-select"
    ) as HTMLSelectElement | null;
    let lang =
      localStorage.getItem("preferredLanguage") ||
      getCookie("preferredLanguage") ||
      "de";
    if (languageSelect) {
      languageSelect.value = lang;
      languageSelect.addEventListener("change", async (e) => {
        const target = e.target as HTMLSelectElement;
        const newLang = target.value;
        await import("../utils/i18n").then((mod) =>
          mod.changeLanguage(newLang)
        );
        window.location.href = window.location.pathname;
      });
    }
  });
</script>
