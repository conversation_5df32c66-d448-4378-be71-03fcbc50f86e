{"name": "llm-browser-astro", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "generate:data": "tsx scripts/generate-static-data.ts", "generate:blog": "node scripts/generate-blog-data.js", "fetch:polyglot": "node scripts/fetch-polyglot-leaderboard.js", "prebuild": "npm run fetch:polyglot && npm run generate:data && npm run generate:blog", "deploy:gitlab": "npm run build && echo 'Ready for GitLab Pages'", "analyze": "cross-env ANALYZE=true astro build --config ./astro.config.dev.mjs", "lint": "eslint . --ext .js,.jsx,.ts,.tsx,.astro", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx,.astro --fix", "lint:check": "eslint . --ext .js,.jsx,.ts,.tsx,.astro --max-warnings 0", "test": "vitest", "test:run": "vitest run", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage"}, "dependencies": {"@astrojs/react": "^4.3.0", "@astrojs/tailwind": "^6.0.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "astro": "^5.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.513.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "sharp": "^0.34.2", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/js": "^9.28.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "@vitejs/plugin-react": "^4.5.2", "@vitest/ui": "^3.2.3", "cross-env": "^7.0.3", "eslint": "^9.28.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^26.1.0", "tsx": "^4.19.4", "typescript-eslint": "^8.34.0", "vite-bundle-analyzer": "^0.22.3", "vitest": "^3.2.3", "yaml": "^2.6.1"}}