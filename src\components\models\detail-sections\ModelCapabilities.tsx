import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../../ui/card";
import { Check, X } from "lucide-react";
import type { EnrichedModelData } from "../types";

interface ModelCapabilitiesProps {
  model: EnrichedModelData;
}

interface CapabilityItemProps {
  label: string;
  isSupported: boolean | null | undefined;
  description?: string;
}

function CapabilityItem({ label, isSupported, description }: CapabilityItemProps) {
  const supported = Boolean(isSupported);
  
  return (
    <div className="flex items-center gap-3 p-3 rounded-lg bg-muted">
      <span className={`inline-flex items-center justify-center w-6 h-6 rounded ${
        supported ? "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400" : "bg-muted text-muted-foreground"
      }`}>
        {supported ? <Check className="w-4 h-4" /> : <X className="w-4 h-4" />}
      </span>
      <div className="flex-1">
        <span className="font-medium">{label}</span>
        {description && <p className="text-sm text-muted-foreground mt-1">{description}</p>}
      </div>
    </div>
  );
}

export function ModelCapabilities({ model }: ModelCapabilitiesProps) {
  const modelCard = model.modelCard;

  // Get capabilities from ModelCard or fallback to API data
  const getCapability = (cardKey: keyof NonNullable<typeof modelCard>['capabilities'], apiKey: keyof EnrichedModelData): boolean | null | undefined => {
    return modelCard?.capabilities?.[cardKey] ?? (model[apiKey] as boolean | null | undefined);
  };

  // Core capabilities
  const coreCapabilities = [
    {
      label: "Function Calling",
      supported: getCapability('functionCalling', 'supportsFunctionCalling'),
      description: "Kann strukturierte Funktionsaufrufe ausführen"
    },
    {
      label: "Vision / Bildverarbeitung",
      supported: getCapability('vision', 'supportsVision'),
      description: "Kann Bilder analysieren und verstehen"
    },
    {
      label: "PDF Support",
      supported: getCapability('pdfSupport', 'supportsPdfInput'),
      description: "Kann PDF-Dokumente direkt verarbeiten"
    },
    {
      label: "Audio Input",
      supported: getCapability('audioInput', 'supportsAudioInput'),
      description: "Kann Audio-Dateien als Eingabe verarbeiten"
    },
    {
      label: "Audio Output",
      supported: getCapability('audioOutput', 'supportsAudioOutput'),
      description: "Kann Audio-Ausgaben generieren"
    },
    {
      label: "System Messages",
      supported: getCapability('systemInstructions', 'supportsSystemMessages'),
      description: "Unterstützt System-Anweisungen für Verhalten"
    }
  ];

  // Advanced capabilities
  const advancedCapabilities = [
    {
      label: "Reasoning",
      supported: getCapability('reasoning', 'supportsReasoning'),
      description: "Erweiterte Reasoning-Fähigkeiten für komplexe Probleme"
    },
    {
      label: "Extended Thinking",
      supported: modelCard?.capabilities.thinking,
      description: "Kann extended thinking für tiefere Analyse nutzen"
    },
    {
      label: "Prompt Caching",
      supported: getCapability('promptCaching', 'supportsPromptCaching'),
      description: "Unterstützt Caching von Prompts für Effizienz"
    },
    {
      label: "Batch Processing",
      supported: modelCard?.capabilities.batchProcessing,
      description: "Kann Batch-Verarbeitung für große Datenmengen"
    },
    {
      label: "Code Execution",
      supported: modelCard?.capabilities.codeExecution,
      description: "Kann Code ausführen und interpretieren"
    },
    {
      label: "Grounding",
      supported: getCapability('grounding', 'supportsWebSearch'),
      description: "Kann externe Datenquellen einbeziehen"
    }
  ];

  // OpenAI specific capabilities
  const openAICapabilities = modelCard?.capabilities.structuredOutputs !== undefined ? [
    {
      label: "Structured Outputs",
      supported: modelCard.capabilities.structuredOutputs,
      description: "Kann strukturierte JSON-Ausgaben garantieren"
    },
    {
      label: "Web Browsing",
      supported: modelCard.capabilities.webBrowsing,
      description: "Kann das Web durchsuchen"
    },
    {
      label: "Code Interpreter",
      supported: modelCard.capabilities.codeInterpreter,
      description: "Integrierter Code-Interpreter für Datenanalyse"
    },
    {
      label: "DALL-E Integration",
      supported: modelCard.capabilities.dalleIntegration,
      description: "Kann DALL-E für Bildgenerierung nutzen"
    },
    {
      label: "Real-time API",
      supported: modelCard.capabilities.realTimeAPI,
      description: "Unterstützt Real-time API für Live-Interaktionen"
    }
  ] : [];

  // Technical capabilities
  const technicalCapabilities = [
    {
      label: "Parallel Function Calling",
      supported: model.supportsParallelFunctionCalling,
      description: "Kann mehrere Funktionen parallel ausführen"
    },
    {
      label: "Response Schema",
      supported: model.supportsResponseSchema,
      description: "Unterstützt vordefinierte Antwort-Schemas"
    },
    {
      label: "Tool Choice",
      supported: model.supportsToolChoice,
      description: "Kann spezifische Tools auswählen"
    },
    {
      label: "Native Streaming",
      supported: model.supportsNativeStreaming,
      description: "Unterstützt natives Streaming von Antworten"
    },
    {
      label: "Assistant Prefill",
      supported: model.supportsAssistantPrefill,
      description: "Kann Antworten mit vordefiniertem Text beginnen"
    },
    {
      label: "Embedding Image Input",
      supported: getCapability('embeddingImageInput', 'supportsEmbeddingImageInput'),
      description: "Kann Bilder in Embeddings umwandeln"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Core Capabilities */}
      <Card>
        <CardHeader>
          <CardTitle>Grundlegende Fähigkeiten</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {coreCapabilities.map((capability, _index) => (
              <CapabilityItem
                key={`core-${capability.label}`}
                label={capability.label}
                isSupported={capability.supported}
                description={capability.description}
              />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Advanced Capabilities */}
      <Card>
        <CardHeader>
          <CardTitle>Erweiterte Fähigkeiten</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {advancedCapabilities.map((capability, _index) => (
              <CapabilityItem
                key={`advanced-${capability.label}`}
                label={capability.label}
                isSupported={capability.supported}
                description={capability.description}
              />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* OpenAI Specific Capabilities */}
      {openAICapabilities.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>OpenAI-spezifische Fähigkeiten</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {openAICapabilities.map((capability, _index) => (
                <CapabilityItem
                  key={`openai-${capability.label}`}
                  label={capability.label}
                  isSupported={capability.supported}
                  description={capability.description}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Technical Capabilities */}
      <Card>
        <CardHeader>
          <CardTitle>Technische Fähigkeiten</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {technicalCapabilities.map((capability, _index) => (
              <CapabilityItem
                key={`technical-${capability.label}`}
                label={capability.label}
                isSupported={capability.supported}
                description={capability.description}
              />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Additional Features */}
      <Card>
        <CardHeader>
          <CardTitle>Zusätzliche Features</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <CapabilityItem
              label="Mehrsprachiger Support"
              isSupported={modelCard?.capabilities.multilingualSupport}
              description="Unterstützt mehrere Sprachen"
            />
            <CapabilityItem
              label="Bildgenerierung"
              isSupported={modelCard?.capabilities.imageGeneration}
              description="Kann Bilder generieren"
            />
            <CapabilityItem
              label="LiteLLM Provisioning"
              isSupported={model['liteLLM-provisioning']}
              description="Wird über LiteLLM bereitgestellt"
            />
            {model.recommendation !== undefined && (
              <CapabilityItem
                label="Empfohlen"
                isSupported={model.recommendation}
                description="Als empfohlenes Modell markiert"
              />
            )}
          </div>
        </CardContent>
      </Card>

      {/* Supported Endpoints */}
      {model.supportedEndpoints && model.supportedEndpoints.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Unterstützte Endpoints</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {model.supportedEndpoints.map((endpoint, _index) => (
                <span key={`endpoint-${endpoint}`} className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 px-3 py-1 rounded-full text-sm">
                  {endpoint}
                </span>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* OpenAI Parameters */}
      {model.supported_openai_params && model.supported_openai_params.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Unterstützte OpenAI Parameter</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {model.supported_openai_params.map((param, _index) => (
                <span key={`param-${param}`} className="bg-muted text-muted-foreground px-3 py-1 rounded-full text-sm font-mono">
                  {param}
                </span>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}