---
import Layout from "../../layouts/Layout.astro";
import { RecommendationsPage } from "../../components/recommendations/RecommendationsPage";
import { CollapsibleHeader } from "../../components/models/CollapsibleHeader";
import type { ModelCardsData } from "../../types/model-cards";
import * as fs from "node:fs/promises";
import * as path from "node:path";
import * as fsSync from "node:fs";

// Load translation data
const currentLang =
  Astro.cookies.get("preferredLanguage")?.value ||
  Astro.request.headers.get("accept-language")?.split(",")[0]?.split("-")[0] ||
  "de";

const translationPath = path.join(
  process.cwd(),
  "public",
  "locales",
  currentLang,
  "i18n.json"
);
const fallbackPath = path.join(
  process.cwd(),
  "public",
  "locales",
  "de",
  "i18n.json"
);

let translations: any = {};
try {
  if (fsSync.existsSync(translationPath)) {
    translations = JSON.parse(fsSync.readFileSync(translationPath, "utf-8"));
  } else {
    translations = JSON.parse(fsSync.readFileSync(fallbackPath, "utf-8"));
  }
} catch (error) {
  console.error("Error loading translations:", error);
  translations = JSON.parse(fsSync.readFileSync(fallbackPath, "utf-8"));
}

// Statische Daten zur Build-Zeit laden (GitLab Pages kompatibel)
// Verwende absoluten Pfad basierend auf dem Projekt-Root
const projectRoot = path.resolve(process.cwd());
const dataPath = path.join(projectRoot, "src", "data");

const modelCardsData = JSON.parse(
  await fs.readFile(path.join(dataPath, "model-cards.json"), "utf-8")
) as ModelCardsData;

// Extrahiere nur die Model Cards
const modelCards = modelCardsData.modelCards;

// Berechne Statistiken
const totalModels = modelCards.length;
const providersSet = new Set(modelCards.map((card) => card.basicInfo.provider));
const totalProviders = providersSet.size;

// Zähle verfügbare Modelle (GA Status)
const availableModels = modelCards.filter(
  (card) => card.basicInfo.status === "GA"
).length;

// Berechne durchschnittliche Kosten
const avgInputCost =
  modelCards.reduce((sum, card) => sum + card.pricing.inputCostPer1MTokens, 0) /
  totalModels;
const avgOutputCost =
  modelCards.reduce(
    (sum, card) => sum + card.pricing.outputCostPer1MTokens,
    0
  ) / totalModels;
---

<Layout
  title={`${translations.recommendations?.title || "Model-Empfehlungen"} - ${translations.site?.title || "LLM Browser"}`}
>
  <main class="container mx-auto px-4 py-8">
    <!-- Kollabierbare Header-Komponente -->
    <CollapsibleHeader
      title={translations.recommendations?.title ||
        "Model-Empfehlungen für Unternehmen"}
      description={translations.recommendations?.description
        ?.replace("{{totalModels}}", totalModels)
        .replace("{{totalProviders}}", totalProviders) ||
        `Intelligente Empfehlungssystem für ${totalModels} LLM-Modelle von ${totalProviders} Anbietern. Basierend auf Standard-Anwendungsfällen in Unternehmen werden die optimalen Modelle für verschiedene Szenarien empfohlen. Berücksichtigt werden Benchmark-Performance, Capabilities, Kosten und weitere Faktoren für fundierte Entscheidungen.`}
      client:only="react"
    >
      <!-- Statistik-Übersicht -->
      <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground">
            {
              translations.recommendations?.availableModels ||
                "Verfügbare Modelle"
            }
          </h3>
          <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">
            {totalModels}
          </p>
        </div>
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground">
            {translations.recommendations?.providers || "Anbieter"}
          </h3>
          <p class="text-3xl font-bold text-green-600 dark:text-green-400">
            {totalProviders}
          </p>
        </div>
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground">
            {translations.recommendations?.gaStatus || "GA Status"}
          </h3>
          <p class="text-3xl font-bold text-purple-600 dark:text-purple-400">
            {availableModels}
          </p>
        </div>
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground">
            {translations.recommendations?.avgInputCost || "⌀ Input Kosten"}
          </h3>
          <p class="text-2xl font-bold text-orange-600 dark:text-orange-400">
            ${avgInputCost.toFixed(2)}
          </p>
          <p class="text-xs text-muted-foreground">
            {translations.recommendations?.perMillion || "pro 1M Tokens"}
          </p>
        </div>
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground">
            {translations.recommendations?.avgOutputCost || "⌀ Output Kosten"}
          </h3>
          <p class="text-2xl font-bold text-red-600 dark:text-red-400">
            ${avgOutputCost.toFixed(2)}
          </p>
          <p class="text-xs text-muted-foreground">
            {translations.recommendations?.perMillion || "pro 1M Tokens"}
          </p>
        </div>
      </div>
    </CollapsibleHeader>

    <!-- React Island für die Empfehlungs-Seite -->
    <RecommendationsPage modelCards={modelCards} client:only="react" />
  </main>
</Layout>

<style>
  .container {
    max-width: 1400px;
  }
</style>
