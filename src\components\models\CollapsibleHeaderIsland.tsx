import { TranslationProvider } from "../../contexts/TranslationContext";
import { CollapsibleHeader } from "./CollapsibleHeader";

interface CollapsibleHeaderIslandProps {
  title: string;
  description: string;
  isCollapsedByDefault?: boolean;
  children: React.ReactNode;
}

export default function CollapsibleHeaderIsland(props: CollapsibleHeaderIslandProps) {
  return (
    <TranslationProvider>
      <CollapsibleHeader {...props} />
    </TranslationProvider>
  );
}