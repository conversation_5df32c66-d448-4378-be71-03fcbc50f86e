{"modelCards": [{"basicInfo": {"modelId": "claude-3-7-sonnet@20250219", "displayName": "Claude 3.7 Sonnet", "provider": "Anthropic", "modelFamily": "<PERSON>", "version": "20250219", "description": "Das bisher intelligenteste Modell von Anthropic und das erste Claude-Modell, das erweitertes Denken bietet – die Fähigkeit, komplexe Probleme mit sorgfältiger, schrittweiser Argumentation zu lösen", "releaseDate": "2025-03-20", "status": "GA", "knowledgeCutoff": "November 2024"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 128000, "architecture": "Transformer", "parameterCount": "<PERSON><PERSON> ver<PERSON><PERSON><PERSON>", "supportedInputTypes": ["text", "image", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/webp"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": true, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 55, "tokensPerMinute": 500000, "contextLength": 200000}, "temperature": {"min": 0, "max": 1, "default": 0.7}}, "pricing": {"inputCostPer1MTokens": 3, "outputCostPer1MTokens": 15, "cachingCosts": {"cacheWrites": 3.75, "cacheHits": 0.3}, "currency": "USD"}, "availability": {"regions": [{"region": "us-east5", "availability": "GA"}, {"region": "europe-west1", "availability": "GA"}, {"region": "global", "availability": "GA"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI", "AWS Bedrock", "Anthropic API"], "platformSpecificIds": {"anthropicApi": "claude-3-7-sonnet-20250219", "awsBedrock": "anthropic.claude-3-7-sonnet-20250219-v1:0", "vertexAi": "claude-3-7-sonnet@20250219"}}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": true, "fixedQuota": true}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 62.3, "alternativeScores": {"multipleAttempts": 70.3}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Mit bash/editor tools, durchschnittlich über 10 Versuche"}, {"benchmarkName": "Terminal-bench", "category": "Agentic terminal coding", "score": 35.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-16", "notes": "Claude Code agent framework, ±1.3% confidence interval"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 78.2, "alternativeScores": {"multipleAttempts": 83.3}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Mit Extended Thinking (bis zu 64k Tokens)"}, {"benchmarkName": "TAU-bench Retail", "category": "Agentic tool use", "score": 81.2, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Mit Extended Thinking und Tool Use"}, {"benchmarkName": "TAU-bench Airline", "category": "Agentic tool use", "score": 58.4, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Mit Extended Thinking und Tool Use"}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 85.9, "metric": "Accuracy", "attemptType": "average", "date": "2025-05-22", "notes": "Durchschnitt über 14 nicht-englische Sprachen, mit Extended Thinking"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 76, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "MMMU benchmark from vals.ai (Claude 3.7 Sonnet Thinking)"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 54.8, "alternativeScores": {"multipleAttempts": 85}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "<PERSON>t Extended Thinking, nucleus sampling top_p=0.95"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 82.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-15", "notes": "MATH 500 benchmark"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 64.9, "alternativeScores": {"withThinking": 64.9, "noThinking": 60.4}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-02-24", "notes": "Aider polyglot benchmark with diff edit format (32k thinking tokens vs no thinking)"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 97.8, "alternativeScores": {"withThinking": 97.8, "noThinking": 93.3}, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-02-24", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1357.13, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #5"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 63.8, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 8.04, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "<PERSON> 3.7 <PERSON><PERSON> (Thinking): 8.04±1.07"}], "metadata": {"lastUpdated": "2025-06-09T18:22:00Z", "dataSource": "Google Cloud Vertex AI Documentation, Anthropic Claude 4 Blog Post, Anthropic Models Overview, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard, MATH 500 Benchmark Update", "version": "1.4"}}, {"basicInfo": {"modelId": "claude-3-5-sonnet-v2@20241022", "displayName": "Claude 3.5 Sonnet v2", "provider": "Anthropic", "modelFamily": "<PERSON>", "version": "20241022", "description": "Ein hochmodernes Modell für reale Softwareentwicklungsaufgaben und für von KI-Agenten zu übernehmende Aufgaben. Setzt neue Industriestandards für Intelligenz mit 2x der Geschwindigkeit von Claude 3 Opus.", "releaseDate": "2024-10-22", "status": "Deprecated", "knowledgeCutoff": "August 2024", "deprecationDate": "2025-06-20", "shutdownDate": "2025-06-20"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 8000, "supportedInputTypes": ["text", "image", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 90, "tokensPerMinute": 540000}, "temperature": {"min": 0, "max": 1, "default": 0.7}}, "pricing": {"inputCostPer1MTokens": 3, "outputCostPer1MTokens": 15, "cachingCosts": {"cacheWrites": 3.75, "cacheHits": 0.3}, "currency": "USD"}, "availability": {"regions": [{"region": "us-east5", "availability": "GA"}, {"region": "europe-west1", "availability": "GA"}, {"region": "global", "availability": "GA"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI", "AWS Bedrock", "Anthropic API"]}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": true, "fixedQuota": true}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 49, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Software engineering benchmark for real-world coding tasks"}, {"benchmarkName": "GPQA Diamond", "category": "Reasoning & Knowledge", "score": 59.4, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-06-21", "notes": "0-shot CoT"}, {"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 88.7, "alternativeScores": {"average": 88.3}, "metric": "Accuracy", "attemptType": "multiple attempts", "date": "2024-06-21", "notes": "5-shot vs 0-shot CoT"}, {"benchmarkName": "MGSM", "category": "Mathematics", "score": 91.6, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-06-21", "notes": "0-shot CoT"}, {"benchmarkName": "DROP", "category": "Reasoning & Knowledge", "score": 87.1, "metric": "F1 Score", "attemptType": "multiple attempts", "date": "2024-06-21", "notes": "3-shot"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 78, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-15", "notes": "MATH 500 benchmark - updated score"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 51.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-17", "notes": "Aider-Polyglot benchmark with diff edit format"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 99.6, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-01-17", "notes": "Percentage of well-formed responses in Aider-Polyglot benchmark"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 37.2, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1150, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #9"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 4.08, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Claude 3.5 Sonnet (October 2024): 4.08±0.78"}], "metadata": {"lastUpdated": "2025-06-09T18:22:00Z", "dataSource": "Google Cloud Vertex AI Documentation, Anthropic Documentation, SWE-bench Verified Benchmark, MATH 500 Benchmark Update", "version": "1.2"}}, {"basicInfo": {"modelId": "gemini-2.5-pro-preview-05-06", "displayName": "Gemini 2.5 Pro", "provider": "Google", "modelFamily": "Gemini", "version": "preview-05-06", "description": "Unser fortschrittlichstes Gemini-Modell für logisches Denken, das komplexe Probleme lösen kann", "releaseDate": "2025-05-06", "status": "Preview", "knowledgeCutoff": "Januar 2025"}, "technicalSpecs": {"contextWindow": 1048576, "maxOutputTokens": 65535, "supportedInputTypes": ["text", "image", "audio", "video", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"maxImages": 3000, "maxImageSize": "7 MB", "maxAudioLength": "8.4 Stunden", "maxVideoLength": "45 Minuten", "supportedMimeTypes": {"image": ["image/png", "image/jpeg", "image/webp"], "audio": ["audio/x-aac", "audio/flac", "audio/mp3", "audio/m4a", "audio/mpeg", "audio/mpga", "audio/mp4", "audio/opus", "audio/pcm", "audio/wav", "audio/webm"], "video": ["video/x-flv", "video/quicktime", "video/mpeg", "video/mpegs", "video/mpg", "video/mp4", "video/webm", "video/wmv", "video/3gpp"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": true, "audioOutput": false, "imageGeneration": false, "codeExecution": true, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": true, "multilingualSupport": true, "embeddingImageInput": true, "liteLLM-provisioning": true}, "performance": {"latency": "Moderately Fast", "rateLimits": {"tokensPerMinute": 1000000}, "temperature": {"min": 0, "max": 2, "default": 1}, "topP": 0.95, "topK": 64}, "pricing": {"inputCostPer1MTokens": 7, "outputCostPer1MTokens": 21, "cachingCosts": {"cacheWrites": 8.75, "cacheHits": 0.7}, "currency": "USD"}, "availability": {"regions": [{"region": "global", "availability": "Preview"}, {"region": "us-central1", "availability": "Preview"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI"]}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR", "ISO27001"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": true, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 17.8, "metric": "Pass Rate", "attemptType": "single attempt", "toolsUsed": false, "date": "2025-06-09", "notes": "Gemini 2.5 Pro Preview (May 06 2025)"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 86.4, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-06"}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 86.7, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-06"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 92, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-06"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 85.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "AIME 2024 and 2025 combined benchmark from vals.ai (Gemini 2.5 Pro Exp)"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 69.2, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 76.9, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-07", "notes": "Aider polyglot benchmark with diff-fenced edit format"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 97.3, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-05-07", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 70.2, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Updated SWE-bench Verified benchmark score"}, {"benchmarkName": "SimpleQA", "category": "Factuality", "score": 52.9, "metric": "Accuracy", "date": "2025-05-06"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 81.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "MMMU benchmark from vals.ai (Gemini 2.5 Pro Exp)"}, {"benchmarkName": "MRCR", "category": "Long context", "score": 91.5, "alternativeScores": {"average": 91.5, "pointwise": 83.1}, "metric": "Accuracy", "contextLength": "128k", "attemptType": "average", "date": "2025-06-09", "notes": "128k average: 91.5%, 1M pointwise: 83.1%"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 25.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-15", "notes": "Terminus agent framework, ±2.8% confidence interval"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1443.22, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #1 (tied)"}], "metadata": {"lastUpdated": "2025-06-09T12:47:00Z", "dataSource": "Google Cloud Vertex AI Documentation, vals.ai AIME Benchmark, vals.ai MMMU Benchmark, SWE-bench Verified Benchmark, Terminal-Bench Leaderboard", "version": "1.4"}}, {"basicInfo": {"modelId": "gpt-4.1", "displayName": "GPT-4.1", "provider": "OpenAI", "modelFamily": "GPT", "version": "2025-08-14", "description": "Flagship GPT model for complex tasks. It is well suited for problem solving across domains", "releaseDate": "2025-01-30", "status": "GA", "knowledgeCutoff": "Juni 2024"}, "technicalSpecs": {"contextWindow": 1047576, "maxOutputTokens": 32768, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": true, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": true, "codeInterpreter": true, "dalleIntegration": true, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 500, "tokensPerMinute": 20000, "batchQueueLimit": 90000}, "temperature": {"min": 0, "max": 2, "default": 1}, "topP": 1}, "pricing": {"inputCostPer1MTokens": 2, "outputCostPer1MTokens": 8, "cachingCosts": {"cacheHits": 0.5}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "OpenAI Batch API"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 54.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Real software engineering tasks"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 66.3, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Graduate-level reasoning in science"}, {"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 90.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Massive Multitask Language Understanding"}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 87.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Multilingual Q&A across 14 languages"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 74.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Multimodal understanding and reasoning"}, {"benchmarkName": "MathVista", "category": "Visual reasoning", "score": 72.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Visual mathematical tasks"}, {"benchmarkName": "CharXiv", "category": "Visual reasoning", "score": 56.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Questions about charts from scientific papers"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 48.1, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-30", "notes": "American Invitational Mathematics Examination"}, {"benchmarkName": "Internal API instruction following (hard)", "category": "Instruction following", "score": 49.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Hard subset of instruction following eval"}, {"benchmarkName": "MultiChallenge", "category": "Instruction following", "score": 38.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Multi-turn instruction following"}, {"benchmarkName": "IFEval", "category": "Instruction following", "score": 87.4, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Instruction following with verifiable instructions"}, {"benchmarkName": "Multi-IF", "category": "Instruction following", "score": 70.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Multi-instruction following benchmark"}, {"benchmarkName": "Graphwalks BFS <128k accuracy", "category": "Long context", "score": 61.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Multi-round co-reference resolution in langen Kontexten"}, {"benchmarkName": "ComplexFuncBench", "category": "Function calling", "score": 65.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Complex function calling benchmark"}, {"benchmarkName": "TAU-bench Airline", "category": "Agentic coding", "score": 49.4, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Agentic tool use in airline scenarios"}, {"benchmarkName": "TAU-bench Retail", "category": "Agentic coding", "score": 68, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Agentic tool use in retail scenarios"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 30.3, "alternativeScores": {"codexCli": 8.3}, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-15", "notes": "Terminus agent: 30.3% ±2.1%, Codex CLI: 8.3% ±1.4%"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 51.6, "alternativeScores": {"diff": 52.9}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Aider polyglot benchmark - whole: 51.6%, diff: 52.9%"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 98.2, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 71, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen (High reasoning effort)"}, {"benchmarkName": "MRCR", "category": "Long context", "score": 48.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Multi-hop Reading Comprehension with Reasoning"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1389.18, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #3"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 5.4, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "GPT-4.1: 5.40±0.89"}], "metadata": {"lastUpdated": "2025-06-09T12:47:00Z", "dataSource": "OpenAI Platform Documentation, Terminal-Bench Leaderboard", "version": "1.2"}}, {"basicInfo": {"modelId": "gpt-4.1-mini-2025-04-14", "displayName": "GPT-4.1 mini", "provider": "OpenAI", "modelFamily": "GPT", "version": "2025-04-14", "description": "Cost-effective version of GPT-4.1 with reduced capabilities for everyday tasks. Balances performance and affordability for high-volume usage.", "releaseDate": "2025-04-14", "status": "GA", "knowledgeCutoff": "Juni 2024"}, "technicalSpecs": {"contextWindow": 1047576, "maxOutputTokens": 32768, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": false, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": true, "codeInterpreter": true, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": false}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 500, "tokensPerMinute": 150000, "batchQueueLimit": 1000000}, "temperature": {"min": 0, "max": 2, "default": 1}}, "pricing": {"inputCostPer1MTokens": 0.5, "outputCostPer1MTokens": 2, "cachingCosts": {"cacheHits": 0.125}, "batchProcessingCosts": {"inputCostPer1MTokens": 0.25, "outputCostPer1MTokens": 1}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "OpenAI Batch API"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 87.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Massive Multitask Language Understanding"}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 78.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multilingual Q&A across 14 languages"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 65, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Graduate-level reasoning in science"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 49.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "American Invitational Mathematics Examination"}, {"benchmarkName": "MathVista", "category": "Visual reasoning", "score": 73.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Visual mathematical tasks"}, {"benchmarkName": "CharXiv", "category": "Visual reasoning", "score": 56.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Questions about charts from scientific papers"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 72.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multimodal understanding and reasoning"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 23.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Real software engineering tasks"}, {"benchmarkName": "Internal API instruction following (hard)", "category": "Instruction following", "score": 45.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Hard subset of instruction following eval"}, {"benchmarkName": "MultiChallenge", "category": "Instruction following", "score": 35.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multi-turn instruction following"}, {"benchmarkName": "IFEval", "category": "Instruction following", "score": 84.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Instruction following with verifiable instructions"}, {"benchmarkName": "Multi-IF", "category": "Instruction following", "score": 67, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multi-instruction following benchmark"}, {"benchmarkName": "Graphwalks BFS <128k accuracy", "category": "Long context", "score": 61.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multi-round co-reference resolution in langen Kontexten"}, {"benchmarkName": "ComplexFuncBench", "category": "Function calling", "score": 49.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Complex function calling benchmark"}, {"benchmarkName": "TAU-bench Airline", "category": "Agentic coding", "score": 36, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Agentic tool use in airline scenarios"}, {"benchmarkName": "TAU-bench Retail", "category": "Agentic coding", "score": 55.8, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Agentic tool use in retail scenarios"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 34.7, "alternativeScores": {"diff": 31.6}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Aider polyglot benchmark - whole: 34.7%, diff: 31.6%"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 47.1, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 3.2, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "GPT-4.1 mini score estimated based on positioning relative to other GPT models"}], "metadata": {"lastUpdated": "2025-06-19T12:55:00Z", "dataSource": "OpenAI Platform Documentation, Model Specifications based on GPT Family patterns", "version": "1.0"}}, {"basicInfo": {"modelId": "gpt-4.1-nano-2025-04-14", "displayName": "GPT-4.1 nano", "provider": "OpenAI", "modelFamily": "GPT", "version": "2025-04-14", "description": "Fastest, most cost-effective GPT-4.1 model", "releaseDate": "2025-04-14", "status": "GA", "knowledgeCutoff": "Jun 01, 2024"}, "technicalSpecs": {"contextWindow": 1047576, "maxOutputTokens": 32768, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": true, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": false, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": false, "codeInterpreter": true, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fastest", "rateLimits": {"queriesPerMinute": 500, "tokensPerMinute": 200000, "batchQueueLimit": 2000000}, "temperature": {"min": 0, "max": 2, "default": 1}}, "pricing": {"inputCostPer1MTokens": 0.1, "outputCostPer1MTokens": 0.4, "cachingCosts": {"cacheHits": 0.025}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "OpenAI Batch API"]}, "benchmarks": [{"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 29.4, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "American Invitational Mathematics Examination"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 50.3, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Graduate-level reasoning in science"}, {"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 80.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Massive Multitask Language Understanding"}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 66.9, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multilingual Q&A across 14 languages"}, {"benchmarkName": "Internal API instruction following (hard)", "category": "Instruction following", "score": 31.6, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Hard subset of instruction following eval"}, {"benchmarkName": "MultiChallenge", "category": "Instruction following", "score": 15, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multi-turn instruction following"}, {"benchmarkName": "IFEval", "category": "Instruction following", "score": 74.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Instruction following with verifiable instructions"}, {"benchmarkName": "Multi-IF", "category": "Instruction following", "score": 57.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multi-instruction following benchmark"}, {"benchmarkName": "Graphwalks BFS <128k accuracy", "category": "Long context", "score": 25, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multi-round co-reference resolution in langen Kontexten"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 55.4, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multimodal understanding and reasoning"}, {"benchmarkName": "MathVista", "category": "Visual reasoning", "score": 56.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Visual mathematical tasks"}, {"benchmarkName": "CharXiv", "category": "Visual reasoning", "score": 40.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Questions about charts from scientific papers"}, {"benchmarkName": "ComplexFuncBench", "category": "Function calling", "score": 5.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Complex function calling benchmark"}, {"benchmarkName": "TAU-bench Airline", "category": "Agentic coding", "score": 14, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Agentic tool use in airline scenarios"}, {"benchmarkName": "TAU-bench Retail", "category": "Agentic coding", "score": 22.6, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Agentic tool use in retail scenarios"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 9.8, "alternativeScores": {"diff": 6.2}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Aider polyglot benchmark - whole: 9.8%, diff: 6.2%"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 94.2, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 23.8, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 1.5, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "GPT-4.1 nano score estimated based on positioning relative to other GPT models"}], "metadata": {"lastUpdated": "2025-06-09T12:31:00Z", "dataSource": "OpenAI Platform Documentation", "version": "1.1"}}, {"basicInfo": {"modelId": "gemini-2.0-flash-001", "displayName": "Gemini 2.0 Flash", "provider": "Google", "modelFamily": "Gemini", "version": "001", "description": "Funktionen der nächsten Generation und verbesserte Fähigkeiten, darunter höhere Geschwindigkeit, integrierte Tools und multimodale Generierung", "releaseDate": "2025-02-05", "status": "Deprecated", "knowledgeCutoff": "Juni 2024", "deprecationDate": "2025-06-20", "shutdownDate": "2025-06-20"}, "technicalSpecs": {"contextWindow": 1048576, "maxOutputTokens": 8192, "supportedInputTypes": ["text", "image", "audio", "video", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"maxImages": 3000, "maxImageSize": "7 MB", "maxAudioLength": "8.4 Stunden", "maxVideoLength": "45 Minuten", "supportedMimeTypes": {"image": ["image/png", "image/jpeg", "image/webp"], "audio": ["audio/x-aac", "audio/flac", "audio/mp3", "audio/m4a", "audio/mpeg", "audio/opus", "audio/wav"], "video": ["video/mp4", "video/webm", "video/quicktime", "video/mpeg"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": true, "audioOutput": false, "imageGeneration": false, "codeExecution": true, "systemInstructions": true, "promptCaching": false, "batchProcessing": true, "reasoning": false, "thinking": false, "grounding": true, "multilingualSupport": true, "embeddingImageInput": false, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"tokensPerMinute": 40000000}, "temperature": {"min": 0, "max": 2, "default": 1}, "topP": 0.95, "topK": 64}, "pricing": {"inputCostPer1MTokens": 1.25, "outputCostPer1MTokens": 5, "currency": "USD"}, "availability": {"regions": [{"region": "global", "availability": "GA"}, {"region": "us-central1", "availability": "GA"}, {"region": "us-east1", "availability": "GA"}, {"region": "us-east4", "availability": "GA"}, {"region": "us-east5", "availability": "GA"}, {"region": "us-south1", "availability": "GA"}, {"region": "us-west1", "availability": "GA"}, {"region": "us-west4", "availability": "GA"}, {"region": "europe-central2", "availability": "GA"}, {"region": "europe-north1", "availability": "GA"}, {"region": "europe-southwest1", "availability": "GA"}, {"region": "europe-west1", "availability": "GA"}, {"region": "europe-west4", "availability": "GA"}, {"region": "europe-west8", "availability": "GA"}, {"region": "europe-west9", "availability": "GA"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI"]}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": true, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 71.9, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "Multimodal multiple-choice questions with images"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 62.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-06-06", "notes": "Graduate-level physics, chemistry, and biology questions"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 89.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-15", "notes": "MATH 500 benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Code generation", "score": 1039.93, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition for web development challenges"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 22.2, "metric": "Pass Rate", "attemptType": "Whole file editing", "date": "2024-12-22", "notes": "Code-Bearbeitung in verschiedenen Programmiersprachen mit verschiedenen Editing-Modi"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 44.2, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Reale Software-Engineering-Aufgaben mit verifizierten GitHub Issues"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 100, "metric": "Percent Cases Well Formed", "attemptType": "Whole file editing", "date": "2024-12-22", "notes": "Prozentsatz der wohlgeformten Antworten bei der mehrsprachigen Code-Bearbeitung"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 31.8, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Bewertung von Large Language Models für Code-Generierung mit kontinuierlich aktualisierten Problemen aus aktuellen Programmierwettbewerben"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 6.56, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Gemini 2.0 Flash Thinking (January 2025): 6.56±0.97"}], "metadata": {"lastUpdated": "2025-06-09T18:22:00Z", "dataSource": "Google Cloud Vertex AI Documentation, VALS.ai, Vellum.ai, WebDev Arena, MATH 500 Benchmark Update", "version": "1.1"}}, {"basicInfo": {"modelId": "claude-opus-4@20250514", "displayName": "<PERSON> 4", "provider": "Anthropic", "modelFamily": "<PERSON>", "version": "20250514", "description": "Das bisher leistungsstärkste Modell von Anthropic und das modernste Codierungsmodell. Claude Opus 4 bietet eine konstante Leistung bei langwierigen Aufgaben, die konzentrierte Anstrengungen und Tausende von Schritten erfordern.", "releaseDate": "2025-05-22", "status": "GA", "knowledgeCutoff": "November 2024"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 32000, "architecture": "Transformer", "supportedInputTypes": ["text", "image", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Moderately Fast", "rateLimits": {"queriesPerMinute": 25, "inputTokensPerMinute": 60000, "outputTokensPerMinute": 6000}, "temperature": {"min": 0, "max": 1, "default": 0.7}}, "pricing": {"inputCostPer1MTokens": 15, "outputCostPer1MTokens": 75, "cachingCosts": {"cacheWrites": 18.75, "cacheHits": 1.5}, "currency": "USD"}, "availability": {"regions": [{"region": "us-east5", "availability": "GA"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI", "AWS Bedrock", "Anthropic API"]}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": true, "fixedQuota": true}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 72.5, "alternativeScores": {"multipleAttempts": 79.4}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Mit bash/editor tools. Alternative Score mit parallel test-time compute."}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 43.2, "alternativeScores": {"multipleAttempts": 50}, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Agentic terminal coding mit Claude Code als agent framework."}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 79.6, "alternativeScores": {"multipleAttempts": 83.3}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Alternative Score mit extended thinking."}, {"benchmarkName": "TAU-bench Retail", "category": "Agentic coding", "score": 81.4, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Agentic tool use benchmark - Retail domain."}, {"benchmarkName": "TAU-bench Airline", "category": "Agentic coding", "score": 59.6, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Agentic tool use benchmark - Airline domain."}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 88.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Multilingual Q&A - Durchschnitt über 14 nicht-englische Sprachen."}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 73.4, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "MMMU benchmark from vals.ai (Claude Opus 4 Nonthinking)"}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 75.5, "alternativeScores": {"multipleAttempts": 90}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "High school math competition. Alternative Score mit extended thinking."}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 72, "alternativeScores": {"withThinking": 72, "noThinking": 70.7}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-25", "notes": "Aider polyglot benchmark with diff edit format (32k thinking tokens vs no thinking)"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 97.3, "alternativeScores": {"withThinking": 97.3, "noThinking": 98.7}, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-05-25", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1411.98, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #1 (tied)"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 77.8, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 6.68, "alternativeScores": {"thinking": 10.72}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "<PERSON> 4: 6.68±0.98, <PERSON> 4 (Thinking): 10.72±1.21"}], "metadata": {"lastUpdated": "2025-06-09T12:47:00Z", "dataSource": "Google Cloud Vertex AI Documentation, Anthropic Blog Post, Anthropic Documentation, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard", "version": "1.2"}}, {"basicInfo": {"modelId": "claude-sonnet-4@20250514", "displayName": "<PERSON> 4", "provider": "Anthropic", "modelFamily": "<PERSON>", "version": "20250514", "description": "Das mittlere Modell von Anthropic mit überlegener Intelligenz für hohes Volumen, z. B. für Programmierung, ausführliche Recherchen und Kundenservicemitarbeiter. <PERSON> 4 bietet eine erhebliche Verbesserung gegenüber Sonnet 3.7 mit verbesserter Codierung und Argumentation.", "releaseDate": "2025-05-22", "status": "GA", "knowledgeCutoff": "März 2025"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 64000, "architecture": "Transformer", "supportedInputTypes": ["text", "image", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 35, "inputTokensPerMinute": 280000, "outputTokensPerMinute": 20000}, "temperature": {"min": 0, "max": 1, "default": 0.7}}, "pricing": {"inputCostPer1MTokens": 3, "outputCostPer1MTokens": 15, "cachingCosts": {"cacheWrites": 3.75, "cacheHits": 0.3}, "currency": "USD"}, "availability": {"regions": [{"region": "us-east5", "availability": "GA"}, {"region": "europe-west1", "availability": "GA"}, {"region": "global", "availability": "GA"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI", "AWS Bedrock", "Anthropic API"]}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": true, "fixedQuota": true}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 72.7, "alternativeScores": {"multipleAttempts": 80.2}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Mit bash/editor tools. Alternative Score mit parallel test-time compute."}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 35.5, "alternativeScores": {"multipleAttempts": 41.3}, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Agentic terminal coding mit Claude Code als agent framework."}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 75.4, "alternativeScores": {"multipleAttempts": 83.8}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Alternative Score mit extended thinking."}, {"benchmarkName": "TAU-bench Retail", "category": "Agentic coding", "score": 80.5, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Agentic tool use benchmark - Retail domain."}, {"benchmarkName": "TAU-bench Airline", "category": "Agentic coding", "score": 60, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Agentic tool use benchmark - Airline domain."}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 86.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Multilingual Q&A - Durchschnitt über 14 nicht-englische Sprachen."}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 75.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "MMMU benchmark from vals.ai (Claude Sonnet 4 Thinking)"}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 70.5, "alternativeScores": {"multipleAttempts": 85}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "High school math competition. Alternative Score mit extended thinking."}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 76.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "AIME 2024 and 2025 combined benchmark from vals.ai (Claude Sonnet 4 Thinking)"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 61.3, "alternativeScores": {"withThinking": 61.3, "noThinking": 56.4}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-24", "notes": "Aider polyglot benchmark with diff edit format (32k thinking tokens vs no thinking)"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 97.3, "alternativeScores": {"withThinking": 97.3, "noThinking": 98.2}, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-05-24", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1389.18, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #3 (tied)"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 70.9, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 5.52, "alternativeScores": {"thinking": 7.76}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "<PERSON> 4: 5.52±0.90, <PERSON> 4 (Thinking): 7.76±1.05"}], "metadata": {"lastUpdated": "2025-06-09T12:47:00Z", "dataSource": "Google Cloud Vertex AI Documentation, Anthropic Blog Post, Anthropic Documentation, vals.ai AIME Benchmark, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard", "version": "1.3"}}, {"basicInfo": {"modelId": "gemini-2.5-flash-preview-05-20", "displayName": "Gemini 2.5 Flash", "provider": "Google", "modelFamily": "Gemini", "version": "preview-05-20", "description": "Unser bestes Modell in Bezug auf Preis und Leistung und bietet umfassende Funktionen. Gemini 2.5 Flash ist unser erstes Flash-Modell mit Denkfunktionen", "releaseDate": "2025-05-20", "status": "Preview", "knowledgeCutoff": "Januar 2025"}, "technicalSpecs": {"contextWindow": 1048576, "maxOutputTokens": 65535, "supportedInputTypes": ["text", "image", "audio", "video", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"maxImages": 3000, "maxImageSize": "7 MB", "maxAudioLength": "8.4 Stunden", "maxVideoLength": "45 Minuten", "supportedMimeTypes": {"image": ["image/png", "image/jpeg", "image/webp"], "audio": ["audio/x-aac", "audio/flac", "audio/mp3", "audio/m4a", "audio/mpeg", "audio/mpga", "audio/mp4", "audio/opus", "audio/pcm", "audio/wav", "audio/webm"], "video": ["video/x-flv", "video/quicktime", "video/mpeg", "video/mpegs", "video/mpg", "video/mp4", "video/webm", "video/wmv", "video/3gpp"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": true, "audioOutput": false, "imageGeneration": false, "codeExecution": true, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": false, "thinking": true, "grounding": true, "multilingualSupport": true, "embeddingImageInput": true, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"tokensPerMinute": 1000000}, "temperature": {"min": 0, "max": 2, "default": 1}, "topP": 0.95, "topK": 64}, "pricing": {"inputCostPer1MTokens": 1.25, "outputCostPer1MTokens": 5, "cachingCosts": {"cacheWrites": 1.56, "cacheHits": 0.13}, "currency": "USD"}, "availability": {"regions": [{"region": "global", "availability": "Preview"}, {"region": "us-central1", "availability": "Preview"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI"]}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR", "ISO27001"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "GPQA Diamond", "category": "Science", "score": 78.3, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Graduate-level reasoning in science"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 44.2, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Software engineering benchmark for real-world coding tasks"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 16.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-17", "notes": "Terminus agent framework, ±1.3% confidence interval"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 55.1, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-25", "notes": "Aider polyglot benchmark with diff edit format (24k thinking tokens)"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 95.6, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-05-25", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1311.55, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #6"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 44.2, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 12.08, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Gemini 2.5 Flash (April 2025): 12.08±1.28"}], "metadata": {"lastUpdated": "2025-06-09T12:47:00Z", "dataSource": "Google Cloud Vertex AI Documentation, SWE-bench Verified Benchmark, Terminal-Bench Leaderboard", "version": "1.2", "variants": [{"modelId": "gemini-2.5-flash-preview-native-audio-dialog", "description": "Native Audio Dialog variant with audio input/output capabilities", "audioOutput": true, "maxOutputTokens": 128000, "supportedInputTypes": ["audio", "video"], "supportedOutputTypes": ["text", "audio"], "status": "Private Preview"}]}}, {"basicInfo": {"modelId": "o3-2025-04-16", "displayName": "o3", "provider": "OpenAI", "modelFamily": "o3", "version": "2025-04-16", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> von o1 - das leistungsstärkste Reasoning-<PERSON><PERSON> von OpenAI, das neue Standards für Mathematik, Wissenschaft, Coding und visuelles Reasoning setzt", "releaseDate": "2025-04-16", "status": "GA", "knowledgeCutoff": "Juni 2024"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 100000, "maxReasoningTokens": 2000000, "maxCompletionTokens": 100000, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": true, "codeExecution": true, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": false, "codeInterpreter": true, "dalleIntegration": true, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Slowest", "rateLimits": {"queriesPerMinute": 500, "tokensPerMinute": 30000, "batchQueueLimit": 90000}, "reasoningPerformance": {"averageReasoningTime": "30-120 Sekunden", "maxReasoningTime": "300 Sekunden", "reasoningEfficiency": "High"}, "temperature": {"min": 0, "max": 2, "default": 1}}, "pricing": {"inputCostPer1MTokens": 2, "outputCostPer1MTokens": 8, "cachingCosts": {"cacheWrites": 2.5, "cacheHits": 0.5}, "batchProcessingCosts": {"inputCostPer1MTokens": 1, "outputCostPer1MTokens": 4}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "Azure OpenAI", "OpenAI Batch API"]}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 69.1, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Ersetzt o1 ab 12.06.2025. Basierend auf Anthropic Claude 4 Vergleichsdaten"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 30.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-15", "notes": "Terminus agent framework, ±0.9% confidence interval"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 83.3, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Basierend auf Anthropic Claude 4 Vergleichsdaten"}, {"benchmarkName": "TAU-bench Retail", "category": "Agentic coding", "score": 70.4, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Basierend auf Anthropic Claude 4 Vergleichsdaten"}, {"benchmarkName": "TAU-bench Airline", "category": "Agentic coding", "score": 52, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Basierend auf Anthropic Claude 4 Vergleichsdaten"}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 88.8, "metric": "Accuracy", "attemptType": "average", "date": "2025-05-22", "notes": "Basierend auf Anthropic Claude 4 Vergleichsdaten"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 80.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "MMMU benchmark from vals.ai"}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 88.9, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Übertroffen von o4-mini (92.7%)"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 91.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Übertroffen von o4-mini (93.4%). Ersetzt o1 ab 12.06.2025."}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 85.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "AIME 2024 and 2025 combined benchmark from vals.ai"}, {"benchmarkName": "MathVista", "category": "Visual reasoning", "score": 86.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Übertrifft o4-mini (84.3%) und o1 (71.8%)"}, {"benchmarkName": "CharXiv", "category": "Visual reasoning", "score": 78.6, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Übertrifft o4-mini (72.0%) und o1 (55.1%)"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 20.32, "alternativeScores": {"high": 20.32, "medium": 19.2}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "o3 (high): 20.32±1.58, o3 (medium): 19.20±1.54"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 79.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Aider polyglot benchmark with diff edit format (high reasoning effort)"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 95.1, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1300, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #6"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 75.9, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}], "metadata": {"lastUpdated": "2025-06-11T08:33:00Z", "dataSource": "DataCamp o4-mini Artikel, OpenAI Documentation, Benchmark-Vergleiche, vals.ai AIME Benchmark, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard, OpenAI Pricing Update June 2025", "version": "1.5"}}, {"basicInfo": {"modelId": "o3-mini-2025-01-31", "displayName": "o3-mini", "provider": "OpenAI", "modelFamily": "o3", "version": "2025-01-31", "description": "Das neueste kleine Reasoning-<PERSON><PERSON> von OpenAI, das hohe Intelligenz bei gleichen Kosten- und Latenzzielen wie o1-mini bietet. WIRD AB 12.06.2025 DURCH O4-MINI ERSETZT.", "releaseDate": "2025-01-31", "status": "DEPRECATED", "knowledgeCutoff": "Oktober 2023"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 100000, "maxReasoningTokens": 100000, "maxCompletionTokens": 100000, "supportedInputTypes": ["text"], "supportedOutputTypes": ["text"]}, "capabilities": {"functionCalling": true, "vision": false, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": true, "codeExecution": true, "systemInstructions": false, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": false, "codeInterpreter": true, "dalleIntegration": true, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 1000, "tokensPerMinute": 100000, "batchQueueLimit": 1000000}, "reasoningPerformance": {"averageReasoningTime": "5-15 Se<PERSON><PERSON>", "maxReasoningTime": "60 Sekunden", "reasoningEfficiency": "High"}, "temperature": {"min": 0, "max": 2, "default": 1}}, "pricing": {"inputCostPer1MTokens": 1.1, "outputCostPer1MTokens": 4.4, "cachingCosts": {"cacheHits": 0.55}, "batchProcessingCosts": {"inputCostPer1MTokens": 0.55, "outputCostPer1MTokens": 2.2}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "Azure OpenAI", "OpenAI Batch API"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 48, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-31", "notes": "Wird durch o4-mini ersetzt (68.1% SWE-bench). o3-mini wird ab 12.06.2025 nicht mehr verfügbar sein."}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 75, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-31", "notes": "Geschätzt basierend auf o3-mini Positionierung"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 86.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "AIME 2024 and 2025 combined benchmark from vals.ai"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 97.9, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-15", "notes": "MATH 500 benchmark"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 78, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-31", "notes": "Geschätzt basierend auf o3-mini Positionierung"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 53.8, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-31", "notes": "Aider polyglot benchmark with diff edit format (medium reasoning effort)"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 95.1, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-01-31", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 69.5, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen (High reasoning effort)"}, {"benchmarkName": "MRCR", "category": "Long context", "score": 36.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Multi-hop Reading Comprehension with Reasoning"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1250, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #7"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 8, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "o3-mini score not in the current leaderboard data, estimated based on positioning"}], "metadata": {"lastUpdated": "2025-06-09T18:22:00Z", "dataSource": "OpenAI Platform Documentation, vals.ai AIME Benchmark, MATH 500 Benchmark Update", "version": "1.2"}}, {"basicInfo": {"modelId": "o3-pro-2025-06-10", "displayName": "o3-pro", "provider": "OpenAI", "modelFamily": "o3", "version": "2025-06-10", "description": "Version of o3 with more compute for better responses. Designed to tackle tough problems with advanced reasoning capabilities.", "releaseDate": "2025-01-31", "status": "GA", "knowledgeCutoff": "Juni 2024"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 100000, "maxReasoningTokens": 100000, "maxCompletionTokens": 100000, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": true, "codeExecution": false, "systemInstructions": true, "promptCaching": false, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": true, "realTimeAPI": false, "liteLLM-provisioning": false}, "performance": {"latency": "Slow", "rateLimits": {"queriesPerMinute": 500, "tokensPerMinute": 30000, "batchQueueLimit": 90000}, "reasoningPerformance": {"averageReasoningTime": "30-120 Sekunden", "maxReasoningTime": "<PERSON><PERSON><PERSON>", "reasoningEfficiency": "High"}, "temperature": {"min": 0, "max": 1, "default": 1}}, "pricing": {"inputCostPer1MTokens": 20, "outputCostPer1MTokens": 80, "reasoningCosts": {"reasoningTokensPerMillion": 20, "completionTokensPerMillion": 80}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API"], "regions": [{"region": "global", "availability": "GA"}]}, "security": {"dataResidency": false, "cmekSupport": false, "vpcSupport": false, "accessTransparency": false, "complianceStandards": ["SOC2"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 88, "metric": "Pass Rate", "attemptType": "single attempt", "notes": "High School Mathematik-Wettbewerb pass@1 evaluation"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 87.7, "metric": "Pass Rate", "attemptType": "single attempt", "notes": "Graduate-level naturwissenschaftliche Fragen mit Extended Thinking"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 71.7, "metric": "Pass@1", "attemptType": "single attempt", "notes": "Live-Code-Generierung ohne Kontamination"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 71, "metric": "Success Rate", "attemptType": "single attempt", "notes": "Reale Software-Engineering-Aufgaben mit Tools"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 79.6, "metric": "Pass Rate", "attemptType": "single attempt", "notes": "Mehrsprachige Code-Bearbeitung mit verschiedenen Editing-Modi"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 95.1, "metric": "Well-formed Rate", "attemptType": "single attempt", "notes": "Prozentsatz der gut strukturierten Code-Antworten bei Aider-Polyglot"}], "metadata": {"lastUpdated": "2025-06-11T08:21:00Z", "dataSource": "OpenAI Documentation", "version": "1.1"}}, {"basicInfo": {"modelId": "gpt-4o-2024-08-06", "displayName": "GPT-4o", "provider": "OpenAI", "modelFamily": "GPT", "version": "2024-08-06", "description": "Vielseitiges, hochintelligentes Flaggschiff-Modell. Akzeptiert Text- und Bildeingaben und produziert Textausgaben. Das beste Modell für die meisten Aufgaben.", "releaseDate": "2024-05-13", "status": "GA", "knowledgeCutoff": "Oktober 2023"}, "technicalSpecs": {"contextWindow": 128000, "maxOutputTokens": 16384, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": true, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": false, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": true, "codeInterpreter": true, "dalleIntegration": true, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 10000, "tokensPerMinute": 30000000, "batchQueueLimit": 5000000000}, "temperature": {"min": 0, "max": 2, "default": 1}, "topP": 1}, "pricing": {"inputCostPer1MTokens": 2.5, "outputCostPer1MTokens": 10, "cachingCosts": {"cacheHits": 1.25}, "batchProcessingCosts": {"inputCostPer1MTokens": 1.25, "outputCostPer1MTokens": 5}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "Azure OpenAI", "OpenAI Batch API"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 57.2, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Software engineering benchmark for real-world coding tasks"}, {"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 88.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-08-06"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 56.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-08-06"}, {"benchmarkName": "DROP", "category": "Reasoning & Knowledge", "score": 83.4, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-08-06"}, {"benchmarkName": "MGSM", "category": "Mathematics", "score": 90.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-08-06"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 60.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-15", "notes": "MATH 500 benchmark - updated score"}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 69.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-08-06"}, {"benchmarkName": "MathVista", "category": "Visual reasoning", "score": 63.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-08-06"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 69.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-05-13", "notes": "Multimodal understanding and reasoning"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 49.3, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-17", "notes": "Aider-Polyglot benchmark with diff edit format"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 99.6, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-01-17", "notes": "Percentage of well-formed responses in Aider-Polyglot benchmark"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 32.9, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Graphwalks BFS <128k accuracy", "category": "Long context", "score": 45.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Multi-round co-reference resolution in langen Kontexten"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1350, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #5"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 2.72, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "GPT-4o (November 2024): 2.72±0.64"}], "metadata": {"lastUpdated": "2025-06-09T18:22:00Z", "dataSource": "OpenAI Platform Documentation, SWE-bench Verified Benchmark, MATH 500 Benchmark Update", "version": "1.2"}}, {"basicInfo": {"modelId": "mistral-large-2411", "displayName": "<PERSON><PERSON><PERSON> Large (24.11)", "provider": "<PERSON><PERSON><PERSON>", "modelFamily": "<PERSON><PERSON><PERSON>", "version": "2411", "description": "Die nächste Version des Modells Mistral Large (24.07) mit verbesserten Funktionen für Schlussfolgerungen und Funktionsaufrufe", "releaseDate": "2024-11-21", "status": "GA", "knowledgeCutoff": "Oktober 2024"}, "technicalSpecs": {"contextWindow": 128000, "maxOutputTokens": 8192, "architecture": "Transformer", "parameterCount": "<PERSON><PERSON> ver<PERSON><PERSON><PERSON>", "supportedInputTypes": ["text"], "supportedOutputTypes": ["text"]}, "capabilities": {"functionCalling": true, "vision": false, "pdfSupport": false, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": false, "batchProcessing": false, "reasoning": true, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": false, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 60, "tokensPerMinute": 400000, "contextLength": 128000}, "temperature": {"min": 0, "max": 1, "default": 0.7}}, "pricing": {"inputCostPer1MTokens": 2, "outputCostPer1MTokens": 6, "currency": "USD"}, "availability": {"regions": [{"region": "us-central1", "availability": "GA"}, {"region": "europe-west4", "availability": "GA"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI"], "platformSpecificIds": {"vertexAi": "mistral-large-2411"}}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 84, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-11-21", "notes": "Allgemeine Wissensbewertung"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 35.1, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 4.52, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Mistral Medium 3: 4.52±0.81 (estimated for Mistral Large 2411)"}], "metadata": {"lastUpdated": "2025-06-08T15:22:00Z", "dataSource": "Google Cloud Vertex AI Documentation", "version": "1.0"}}, {"basicInfo": {"modelId": "o4-mini-2025-04-16", "displayName": "o4-mini", "provider": "OpenAI", "modelFamily": "o4", "version": "2025-04-16", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> von o3-mini mit deutlich verbesserter Performance. Faster, more affordable reasoning model mit Vision-Support und Web-Browsing.", "releaseDate": "2025-04-16", "status": "GA", "knowledgeCutoff": "Juni 2024"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 100000, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"]}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": false, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": true, "codeInterpreter": true, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Moderately Fast", "rateLimits": {"queriesPerMinute": 1000, "tokensPerMinute": 100000, "batchQueueLimit": 1000000}}, "pricing": {"inputCostPer1MTokens": 1.1, "outputCostPer1MTokens": 4.4, "cachingCosts": {"cacheHits": 0.275}, "batchProcessingCosts": {"inputCostPer1MTokens": 0.55, "outputCostPer1MTokens": 2.2}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "OpenAI Batch API"]}, "benchmarks": [{"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 93.4, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "No tools verwendet. Übertrifft o3 (91.6%), o3-mini (86.5%) und o1 (74.3%). Ersetzt o3-mini ab 12.06.2025."}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 92.7, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "No tools verwendet. Übertrifft o3 (88.9%), o3-mini (86.5%) und o1 (79.2%). Ersetzt o3-mini ab 12.06.2025."}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 83.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "AIME 2024 and 2025 combined benchmark from vals.ai"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 68.1, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Knapp hinter o3 (69.1%), aber deutlich über o1 (48.9%) und o3-mini (49.3%)"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 79.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "MMMU benchmark from vals.ai"}, {"benchmarkName": "MathVista", "category": "Visual reasoning", "score": 84.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Hinter o3 (86.8%), aber deutlich über o1 (71.8%)"}, {"benchmarkName": "CharXiv", "category": "Visual reasoning", "score": 72, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Hinter o3 (78.6%), aber über o1 (55.1%)"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 81.4, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Hinter o3 (83.3%), aber über o1 (78.0%) und o3-mini (77.0%)"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 27.5, "alternativeScores": {"terminus": 18.5, "codexCli": 20}, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-18", "notes": "Goose agent: 27.5% ±1.3%, Terminus: 18.5% ±1.4%, Codex CLI: 20.0% ±1.5%"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 72, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Aider polyglot benchmark with diff edit format (high reasoning effort)"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 90.7, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1099.59, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #19"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 68.3, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 18.08, "alternativeScores": {"high": 18.08, "medium": 14.28}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "o4-mini (high): 18.08±1.51, o4-mini (medium): 14.28±1.37"}], "metadata": {"lastUpdated": "2025-06-09T12:47:00Z", "dataSource": "DataCamp o4-mini Artikel, OpenAI Platform Documentation, vals.ai AIME Benchmark, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard", "version": "1.4"}}, {"basicInfo": {"modelId": "gpt-4o-mini-2024-07-18", "displayName": "GPT-4o mini", "provider": "OpenAI", "modelFamily": "GPT", "version": "2024-07-18", "description": "Fast, affordable small model for focused tasks", "releaseDate": "2024-07-18", "status": "GA", "knowledgeCutoff": "Oktober 2023"}, "technicalSpecs": {"contextWindow": 128000, "maxOutputTokens": 16384, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": false, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": true, "codeInterpreter": true, "dalleIntegration": true, "realTimeAPI": false, "liteLLM-provisioning": false}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 500, "tokensPerMinute": 200000, "batchQueueLimit": 2000000}, "temperature": {"min": 0, "max": 2, "default": 1}, "topP": 1}, "pricing": {"inputCostPer1MTokens": 0.15, "outputCostPer1MTokens": 0.6, "cachingCosts": {"cacheHits": 0.075}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "Azure OpenAI", "OpenAI Batch API"]}, "benchmarks": [{"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 82, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-07-18"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 40.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-07-18"}, {"benchmarkName": "DROP", "category": "Reasoning & Knowledge", "score": 79.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-07-18"}, {"benchmarkName": "MGSM", "category": "Mathematics", "score": 87, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-07-18"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 70.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-07-18"}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 59.4, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-07-18"}, {"benchmarkName": "MathVista", "category": "Visual reasoning", "score": 56.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-07-18"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 3.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2024-12-21", "notes": "Aider-Polyglot benchmark with whole edit format"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 100, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2024-12-21", "notes": "Percentage of well-formed responses in Aider-Polyglot benchmark"}, {"benchmarkName": "Graphwalks BFS <128k accuracy", "category": "Long context", "score": 28.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Multi-round co-reference resolution in langen Kontexten"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1100, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #10"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 18.5, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 2, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "GPT-4o mini score estimated based on positioning relative to GPT-4o"}], "metadata": {"lastUpdated": "2025-06-08T16:17:31Z", "dataSource": "OpenAI Platform Documentation", "version": "1.0"}}, {"basicInfo": {"modelId": "qwen2.5-coder-32b-instruct", "displayName": "Qwen2.5-Coder-32B-Instruct", "provider": "Alibaba", "modelFamily": "<PERSON><PERSON>", "version": "2.5", "description": "Ein spezialisiertes Code-Modell der Qwen2.5-Familie mit 32 Milliarden Parametern, optimiert für Code-Generierung, Code-Verständnis und programmierungsbezogene Aufgaben", "releaseDate": "2024-11-12", "status": "GA", "knowledgeCutoff": "September 2024"}, "technicalSpecs": {"contextWindow": 33000, "maxOutputTokens": 8192, "architecture": "Transformer", "parameterCount": "32B", "supportedInputTypes": ["text"], "supportedOutputTypes": ["text"]}, "capabilities": {"functionCalling": true, "vision": false, "pdfSupport": false, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": false, "batchProcessing": false, "reasoning": true, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": false, "structuredOutputs": true, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "temperature": {"min": 0, "max": 2, "default": 0.7}, "topP": 0.8, "topK": 20}, "pricing": {"inputCostPer1MTokens": 0.07, "outputCostPer1MTokens": 0.16, "currency": "USD"}, "availability": {"supportedPlatforms": ["Lambda AI Inference"], "platformSpecificIds": {"lambdaAi": "qwen25-coder-32b-instruct"}}, "benchmarks": [{"benchmarkName": "GPQA Diamond", "category": "Science", "score": 46, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Graduate-level reasoning in science"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 31.4, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 16.4, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Aider polyglot benchmark with whole edit format"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 99.6, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 902.25, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #31"}], "metadata": {"lastUpdated": "2025-06-08T21:06:00Z", "dataSource": "<PERSON><PERSON>, Benchmark Results, Lambda AI Documentation", "version": "1.0"}}, {"basicInfo": {"modelId": "deepseek-r1-0528", "displayName": "DeepSeek-R1", "provider": "DeepSeek", "modelFamily": "DeepSeek", "version": "0528", "description": "Ein fortschrittliches Reasoning-Modell mit verbesserter Denktiefe und Inferenz-Fähigkeiten, das mit führenden Modellen wie O3 und Gemini 2.5 Pro konkurriert", "releaseDate": "2025-01-20", "status": "GA", "knowledgeCutoff": "Oktober 2024"}, "technicalSpecs": {"contextWindow": 65536, "maxOutputTokens": 65536, "architecture": "MoE", "parameterCount": "671B", "supportedInputTypes": ["text"], "supportedOutputTypes": ["text"]}, "capabilities": {"functionCalling": true, "vision": false, "pdfSupport": false, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": false, "promptCaching": true, "batchProcessing": false, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": false, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Slow", "reasoningPerformance": {"averageReasoningTime": "Variable, a<PERSON><PERSON><PERSON><PERSON><PERSON> von Komplexität", "reasoningEfficiency": "High"}, "temperature": {"min": 0, "max": 2, "default": 0.6}, "topP": 0.95}, "pricing": {"inputCostPer1MTokens": 0.55, "outputCostPer1MTokens": 2.19, "cachingCosts": {"cacheHits": 0.14}, "currency": "USD"}, "availability": {"supportedPlatforms": ["DeepSeek API"]}, "security": {"complianceStandards": ["MIT License"]}, "benchmarks": [{"benchmarkName": "GPQA Diamond", "category": "Science", "score": 71.5, "metric": "Pass@1", "attemptType": "pass@1", "date": "2025-01-20"}, {"benchmarkName": "SimpleQA", "category": "Factuality", "score": 30.1, "metric": "Correct", "attemptType": "single attempt", "date": "2025-01-20"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 8.5, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "DeepSeek-R1 score not in the current leaderboard data, keeping previous value"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 62.8, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 49.2, "metric": "Resolved", "attemptType": "single attempt", "date": "2025-01-20"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 56.9, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-20", "notes": "Aider polyglot benchmark with diff edit format"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 96.9, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-01-20", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 79.8, "metric": "Pass@1", "attemptType": "pass@1", "date": "2025-01-20"}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 70, "metric": "Pass@1", "attemptType": "pass@1", "date": "2025-01-20"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 97.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-15", "notes": "MATH 500 benchmark"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 74, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "AIME 2024 and 2025 combined benchmark from vals.ai"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 5.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-15", "notes": "Terminus agent framework, ±0.7% confidence interval"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1200, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #8"}], "metadata": {"lastUpdated": "2025-06-09T18:22:00Z", "dataSource": "DeepSeek API Documentation, vals.ai AIME Benchmark, Terminal-Bench Leaderboard, MATH 500 Benchmark Update", "version": "1.3"}}, {"basicInfo": {"modelId": "deepseek-v3@20241226", "displayName": "DeepSeek-V3", "provider": "DeepSeek", "modelFamily": "DeepSeek", "version": "V3", "description": "Ein starkes Mixture-of-Experts (MoE) Sprachmodell mit 671B Gesamtparametern und 37B aktivierten Parametern pro Token. Nutzt Multi-head Latent Attention (MLA) und DeepSeekMoE-Architekturen für effiziente Inferenz und kosteneffektives Training.", "releaseDate": "2024-12-26", "status": "GA", "knowledgeCutoff": "Unbekannt"}, "technicalSpecs": {"contextWindow": 128000, "maxOutputTokens": 8192, "architecture": "MoE Transformer", "parameterCount": "671B (37B aktiviert)", "supportedInputTypes": ["text", "image", "audio", "video", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"], "audio": ["audio/wav", "audio/mp3", "audio/flac", "audio/m4a"], "video": ["video/mp4", "video/webm", "video/quicktime"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": true, "audioOutput": false, "imageGeneration": false, "codeExecution": true, "systemInstructions": true, "promptCaching": false, "batchProcessing": true, "reasoning": true, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": false, "structuredOutputs": true, "webBrowsing": false, "codeInterpreter": true, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": false}, "performance": {"latency": "Fast", "temperature": {"min": 0, "max": 2, "default": 1}}, "pricing": {"inputCostPer1MTokens": 0.14, "outputCostPer1MTokens": 0.28, "cachingCosts": {"cacheWrites": 0.14, "cacheHits": 0.014}, "currency": "USD"}, "availability": {"supportedPlatforms": ["HuggingFace", "DeepSeek API"]}, "benchmarks": [{"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 88.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Chat-Version des Modells"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 90.2, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Chat-Version des Modells"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 39.2, "metric": "Pass@1", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Chat-Version des Modells"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 42, "metric": "Resolved", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Chat-Version des Modells"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 49.6, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Chat-Version des Modells"}, {"benchmarkName": "DROP", "category": "Reasoning & Knowledge", "score": 89, "metric": "F1", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Base-Version des Modells"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 64.8, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-30", "notes": "DeepSeek V3 0324 Version - Vellum AI Leaderboard"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 27.2, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-05-30", "notes": "454 problems selected, Rank 21/22 - LiveCodeBench Leaderboard"}, {"benchmarkName": "WebDev-Arena", "category": "Code generation", "score": 959.75, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-05-30", "notes": "Real-time AI coding competition, Rank #29 - LMArena WebDev Arena"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 5.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-15", "notes": "Rank 15/15 using Terminus framework - Terminal-Bench Leaderboard"}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 59.4, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-30", "notes": "DeepSeek V3 0324 Version - Vellum AI Leaderboard"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 42.9, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "Qwen2.5-VL-32B benchmark data - Vellum AI Leaderboard"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 4.55, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-10", "notes": "DeepSeek-V3 nicht im verfügbaren Leaderboard gelistet - Scale AI Leaderboard"}], "metadata": {"lastUpdated": "2025-06-10T11:33:00Z", "dataSource": "HuggingFace DeepSeek-V3 Model Card, DeepSeek-V3 Technical Report, Vals.ai AIME & MMMU Benchmarks, SWE-bench Leaderboard, Terminal-Bench Leaderboard, WebDev-Arena Leaderboard, LiveCodeBench Leaderboard, Vellum AI Leaderboard", "version": "1.1"}}, {"basicInfo": {"modelId": "grok-3", "displayName": "Grok 3", "provider": "xAI", "modelFamily": "Grok", "version": "beta", "description": "Das bisher leistungsstärkste Modell von xAI, das bei Unternehmensanwendungen wie Datenextraktion, Programmierung und Textzusammenfassung glänzt. Verfügt über tiefes Fachwissen in Finanzen, Gesundheitswesen, Recht und Wissenschaft.", "releaseDate": "2024-12-01", "status": "Beta", "knowledgeCutoff": "November 2024"}, "technicalSpecs": {"contextWindow": 131072, "maxOutputTokens": 32768, "architecture": "Transformer", "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"maxImageSize": "10 MB", "supportedMimeTypes": {"image": ["image/jpeg", "image/jpg", "image/png"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": false, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": false, "reasoning": true, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": false, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": false}, "performance": {"latency": "Moderately Fast", "rateLimits": {"queriesPerMinute": 50, "inputTokensPerMinute": 100000, "outputTokensPerMinute": 10000}, "temperature": {"min": 0, "max": 1, "default": 0.7}}, "pricing": {"inputCostPer1MTokens": 3, "outputCostPer1MTokens": 15, "cachingCosts": {"cacheWrites": 3, "cacheHits": 0.75}, "currency": "USD"}, "availability": {"regions": [{"region": "global", "availability": "Beta"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["xAI API"]}, "security": {"dataResidency": false, "cmekSupport": false, "vpcSupport": false, "accessTransparency": false, "complianceStandards": []}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 93.3, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-30", "notes": "High School Mathematik-Wettbewerb 2025 - Grok 3 [Beta] bei vellum.ai"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 84.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-06", "notes": "Graduate-level naturwissenschaftliche Fragen bei vellum.ai"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 66.7, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-10", "notes": "Grok-3-Mini (High) - Holistische und kontaminationsfreie Code-Generierung"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1142.79, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "early-grok-3 - Real-time web development coding competition. Rank #15"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 17.5, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-17", "notes": "grok-3 - Terminal-basierte Coding-Aufgaben. 17.5% ± 4.2%"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 53.3, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-10", "notes": "Grok 3 Beta - Mehrsprachige Code-Bearbeitung mit verschiedenen Editing-Modi"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 99.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-10", "notes": "Grok 3 Beta - Prozentsatz wohlgeformter Antworten in Polyglot-Tests"}], "metadata": {"lastUpdated": "2025-06-10T16:44:00Z", "dataSource": "xAI Documentation, vals.ai AIME Benchmark, vellum.ai LLM Leaderboard, LiveCodeBench Leaderboard, WebDev-Arena Leaderboard, Terminal-Bench Leaderboard, Aider Polyg<PERSON>", "version": "1.4"}}, {"basicInfo": {"modelId": "magistral-medium-2506", "displayName": "Magistral Medium", "provider": "<PERSON><PERSON><PERSON>", "modelFamily": "Magistral", "version": "2506", "description": "Das erste Reasoning-<PERSON><PERSON> von Mistral AI mit überlegenen domänenspezifischen, transparenten und mehrsprachigen Reasoning-Fähigkeiten. Magistral Medium ist die leistungsstärkere Enterprise-Version.", "releaseDate": "2025-06-10", "status": "Preview", "knowledgeCutoff": "Nicht spezifiziert"}, "technicalSpecs": {"contextWindow": 128000, "maxOutputTokens": 40000, "architecture": "Transformer", "supportedInputTypes": ["text"], "supportedOutputTypes": ["text"], "inputLimitations": {"maxContextEffective": "40k (Performance kann nach 40k degradieren)"}}, "capabilities": {"functionCalling": false, "vision": false, "pdfSupport": false, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": false, "batchProcessing": false, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": false, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 50, "tokensPerMinute": 500000}, "reasoningPerformance": {"averageReasoningTime": "Variable", "reasoningEfficiency": "High"}, "temperature": {"min": 0, "max": 1, "default": 0.7}}, "pricing": {"inputCostPer1MTokens": 3, "outputCostPer1MTokens": 15, "currency": "USD"}, "availability": {"regions": [{"region": "global", "availability": "Preview"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["La Plateforme", "<PERSON>", "Amazon SageMaker", "IBM WatsonX", "Azure AI", "Google Cloud Marketplace"]}, "security": {"dataResidency": false, "cmekSupport": false, "vpcSupport": false, "accessTransparency": false, "complianceStandards": ["Apache 2.0"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 73.6, "alternativeScores": {"multipleAttempts": 90}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-10", "notes": "73.6% mit single attempt, 90% mit majority voting @64"}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 64.9, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-10", "notes": "Magistral Medium Performance auf AIME 2025"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 70.8, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-10", "notes": "Graduate-level naturwissenschaftliche Fragen"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 59.4, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-10", "notes": "Aktuelle Live-Code-Generierung ohne Kontamination"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 47.1, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-10", "notes": "Mehrsprachige Code-Bearbeitung Performance"}], "metadata": {"lastUpdated": "2025-06-15T11:25:00Z", "dataSource": "Mistral AI Blog Post, Ollama Documentation", "version": "1.0"}}], "totalCards": 22, "lastUpdated": "2025-06-21T08:40:19.137Z", "generatedFrom": "Individual model files in src/data/models/", "version": "1.0"}