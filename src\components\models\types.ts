import type { ModelData } from "../../types/api";
import type { ModelCard } from "../../types/model-cards";

// Extended ModelData type to include model card
export interface EnrichedModelData extends ModelData {
  modelCard?: ModelCard;
}

export type SortField = "name" | "provider" | "modelGroup" | "contextWindow" | "maxOutputTokens" | "inputCostPerToken" | "outputCostPerToken" | "benchmarkScore";
export type SortDirection = "asc" | "desc";
export type SecurityFilter = "Alle" | "confidential" | "internal" | "open" | "public";
export type ModeFilter = "Alle" | "Chat" | "completion" | "embedding" | "image generation";