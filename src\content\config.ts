import { defineCollection, z } from 'astro:content';

const blog = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    excerpt: z.string(),
    category: z.enum(['model-analysis', 'release-notes', 'benchmark-analysis', 'industry-news']),
    tags: z.array(z.string()),
    publishDate: z.coerce.date(),
    lastUpdated: z.coerce.date().optional(),
    author: z.object({
      name: z.string(),
      role: z.string(),
    }),
    readingTime: z.number().optional(),
    featured: z.boolean().default(false),
    
    // Model-specific fields
    relatedModelIds: z.array(z.string()).optional(),
    relatedBenchmarks: z.array(z.string()).optional(),
    
    // Release notes specific
    releaseVersion: z.string().optional(),
    changelog: z.array(z.object({
      type: z.enum(['added', 'changed', 'deprecated', 'removed', 'fixed', 'security']),
      description: z.string(),
      impact: z.enum(['major', 'minor', 'patch']),
      technicalDetails: z.string().optional(),
    })).optional(),
    
    // SEO
    metaDescription: z.string().optional(),
    metaKeywords: z.array(z.string()).optional(),
    
    // Media
    featuredImage: z.string().optional(),
    gallery: z.array(z.string()).optional(),
  }),
});

export const collections = {
  blog,
};