---
import "../styles/globals.css";
import { TranslationProvider } from "../contexts/TranslationContext";
import type { Translations } from "../contexts/TranslationContext";
import fs from "fs";
import path from "path";
import { ThemeToggleIsland } from "@/components/ThemeToggleIsland";
import { LanguageSelectorIsland } from "@/components/LanguageSelectorIsland";

interface Props {
  title: string;
  description?: string;
}

// Define the expected structure of our translations
interface TranslationsStructure {
  site?: {
    title?: string;
    description?: string;
  };
  nav?: {
    models?: string;
    blog?: string;
    recommendations?: string;
    benchmarks?: string;
    api_usage?: string;
  };
  footer?: {
    copyright?: string;
    built_with?: string;
  };
  meta_description?: string;
  vise_coding?: string;
  language_selector?: {
    label?: string;
    de?: string;
    en?: string;
    pl?: string;
  };
  models?: {
    header?: string;
    description?: string;
    filters?: Record<string, string>;
    stats?: Record<string, string>;
    comparison?: {
      title?: string;
      reset_selection?: string;
      property?: string;
      provider?: string;
      litellm_availability?: string;
      available?: string;
      model_card_only?: string;
      context_window?: string;
      max_output_tokens?: string;
      input_cost?: string;
      output_cost?: string;
      yes?: string;
      no?: string;
      capabilities?: string;
      supported_platforms?: string;
      metric?: string;
      range?: string;
      no_details_available?: string;
      other_benchmarks?: string;
      at?: string;
      and?: string;
      well_formed_code?: string;
      category?: string;
      difficulty?: string;
      variants?: string;
      not_available?: string;
      aider_polyglot_short?: string;
      website?: string;
      paper?: string;
      aider_benchmark?: {
        title?: string;
        description?: string;
        metric?: string;
        range?: string;
        fallback_description?: string;
      };
      [key: string]: any;
    };
    capabilities?: Record<string, string>;
    [key: string]: any;
  };
  components?: {
    collapsible_header?: {
      show_info?: string;
      hide_info?: string;
    };
    [key: string]: any;
  };
  [key: string]: any; // Allow for other properties
}

// Detect language from cookie or browser language on the server side
const cookieLang = Astro.cookies?.get("preferredLanguage")?.value;
const browserLang = Astro.request.headers.get("accept-language")?.split(",")[0]?.split("-")[0];

// Priority: cookie > browser language > default (de)
let currentLang = cookieLang;
if (!currentLang && browserLang && ["de", "en", "pl"].includes(browserLang)) {
  currentLang = browserLang;
}
if (!currentLang) {
  currentLang = "de";
}

// Safely load translations
let translations: TranslationsStructure = {};
try {
  const translationsPath = path.join(
    process.cwd(),
    "public",
    "locales",
    currentLang,
    "i18n.json",
  );

  // Read the file and trim any whitespace
  const fileContent = fs.readFileSync(translationsPath, "utf-8").trim();
  translations = JSON.parse(fileContent);
} catch (error) {
  console.error("Error loading translations:", error);
  // Fallback to empty translations object
}

const { title, description = translations.meta_description || "LLM Browser" } =
  Astro.props;
---

<!doctype html>
<html lang={currentLang}>
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>
  </head>
  <body class="min-h-screen bg-background text-foreground">
    <script is:inline>
      // Theme initialization script to prevent flash
      (function () {
        const theme = localStorage.getItem("theme") || "light";
        if (theme === "dark") {
          document.documentElement.classList.add("dark");
        }
      })();
    </script>
    <!-- Navigation Header -->
    <header
      class="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
    >
      <div class="container mx-auto px-4 py-4">
        <nav class="flex items-center justify-between">
          <div class="flex items-center space-x-6">
            <a
              href="/"
              class="text-xl font-bold text-foreground hover:text-primary"
            >
              {translations.site?.title || "LLM Browser"}
            </a>
            <div class="hidden md:flex space-x-4">
              <a
                href="/models/"
                class="text-muted-foreground hover:text-foreground px-3 py-2 rounded-md text-sm font-medium"
              >
                {translations.nav?.models || "Models"}
              </a>
              <a
                href="/blog/"
                class="text-muted-foreground hover:text-foreground px-3 py-2 rounded-md text-sm font-medium"
              >
                {translations.nav?.blog || "Blog"}
              </a>
              <a
                href="/recommendations/"
                class="text-muted-foreground hover:text-foreground px-3 py-2 rounded-md text-sm font-medium"
              >
                {translations?.nav?.recommendations || "Recommendations"}
              </a>
              <div class="relative group">
                <a
                  href="/benchmarks/"
                  class="text-muted-foreground hover:text-foreground px-3 py-2 rounded-md text-sm font-medium flex items-center"
                >
                  {translations.nav?.benchmarks || "Benchmarks"}
                  <svg
                    class="ml-1 h-3 w-3"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"></path>
                  </svg>
                </a>
                <div
                  class="absolute left-0 mt-1 w-48 rounded-md shadow-lg bg-popover ring-1 ring-border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50"
                >
                  <div class="py-1">
                    <a
                      href="/benchmarks/"
                      class="block px-4 py-2 text-sm text-popover-foreground hover:bg-accent hover:text-accent-foreground"
                    >
                      {translations.nav?.benchmarks || "All Benchmarks"}
                    </a>
                    <a
                      href="/benchmarks/aider-polyglot"
                      class="block px-4 py-2 text-sm text-popover-foreground hover:bg-accent hover:text-accent-foreground"
                    >
                      Aider-Polyglot
                    </a>
                  </div>
                </div>
              </div>
              <a
                href="/blog/api-nutzung-iteragpt-proxy"
                class="text-muted-foreground hover:text-foreground px-3 py-2 rounded-md text-sm font-medium"
              >
                {translations.nav?.api_usage || "API Usage@iteratec"}
              </a>
            </div>
          </div>
          <div class="flex items-center gap-4">
            <!-- Language Selector -->
            <LanguageSelectorIsland client:load />
            <!-- Theme Toggle -->
            <ThemeToggleIsland client:load />
            <!-- GitLab and Release Notes Links -->
            <div class="flex items-center gap-3">
              <a
                href="https://iteragit.iteratec.de/taskforce-genai/ai/vise-coding/iteratec-llm-browser"
                target="_blank"
                rel="noopener noreferrer"
                class="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
                title="GitLab Repository"
              >
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="m22 13.29-3.33-10a.42.42 0 0 0-.14-.18.38.38 0 0 0-.22-.11.39.39 0 0 0-.23.07.42.42 0 0 0-.14.18l-2.26 6.67H8.32L6.1 3.26a.42.42 0 0 0-.1-.18.38.38 0 0 0-.26-.08.39.39 0 0 0-.23.07.42.42 0 0 0-.14.18L2 13.29a.74.74 0 0 0 .27.83L12 21l9.69-6.88a.71.71 0 0 0 .31-.83Z"
                  ></path>
                </svg>
              </a>
              <a
                href="/blog/llm-browser-release-notes"
                class="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
                title="Release Notes"
              >
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect>
                  <path
                    d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"
                  ></path>
                  <path d="m9 14 2 2 4-4"></path>
                </svg>
              </a>
            </div>
            <div class="flex items-center gap-2">
              <img
                id="iteratec-logo"
                src="/images/logo-iteratec.png"
                alt="iteratec Logo"
                class="w-auto"
                style="height: 15px;"
              />
            </div>
          </div>
        </nav>
      </div>
    </header>

    <!-- Main Content -->
    <script define:vars={{ translations, currentLang }}>
      // Store translations in a global variable to ensure proper serialization
      window.__TRANSLATIONS__ = JSON.parse(JSON.stringify(translations));
      window.__CURRENT_LANG__ = currentLang;
    </script>

    <TranslationProvider
      initialTranslations={translations as Translations}
      initialLang={currentLang}
      client:only="react"
    >
      <slot />
    </TranslationProvider>

    <!-- Footer -->
    <footer class="border-t bg-muted mt-16">
      <div class="container mx-auto px-4 py-8">
        <div class="text-center text-muted-foreground">
          <p>{translations.footer?.copyright || "© 2025 LLM Browser"}</p>
          <p class="text-sm mt-2">
            {translations.footer?.built_with || "Built with Astro, React, TypeScript and Tailwind CSS"}
          </p>
        </div>
      </div>
    </footer>

    <!-- Theme-aware logo script -->
    <script is:inline>
      function updateLogo() {
        const logo = document.getElementById("iteratec-logo");
        if (!logo) return;

        // Check current theme from html element class or localStorage
        const isDark =
          document.documentElement.classList.contains("dark") ||
          localStorage.getItem("theme") === "dark";

        logo.src = isDark
          ? "/images/logo-iteratec-darktheme.png"
          : "/images/logo-iteratec.png";
      }

      // Update logo on initial load
      updateLogo();

      // Listen for theme changes
      const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
          if (
            mutation.type === "attributes" &&
            mutation.attributeName === "class"
          ) {
            updateLogo();
          }
        });
      });

      // Start observing theme changes on the html element
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ["class"],
      });

      // Also listen for storage changes (in case theme is changed in another tab)
      window.addEventListener("storage", function (e) {
        if (e.key === "theme") {
          updateLogo();
        }
      });
    </script>
  </body>
</html>
