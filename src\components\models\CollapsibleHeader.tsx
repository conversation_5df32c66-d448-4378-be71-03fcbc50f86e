import * as React from "react";
import { useState } from "react";
import { Button } from "../ui/button";
import { ChevronUp, ChevronDown } from "lucide-react";
import { useTranslation, t } from "../../contexts/TranslationContext";
import type { Translations } from "../../contexts/TranslationContext";

// Define global window interface
declare global {
  interface Window {
    translations?: any;
    currentLang?: string;
  }
}

interface CollapsibleHeaderProps {
  children: React.ReactNode;
  title: string;
  description: string;
  isCollapsedByDefault?: boolean;
}

export function CollapsibleHeader({
  children,
  title,
  description,
  isCollapsedByDefault = false
}: CollapsibleHeaderProps) {
  const [isCollapsed, setIsCollapsed] = useState(isCollapsedByDefault);
  const { translations } = useTranslation();

  // Helper function with fallback logic
  const getTranslation = (key: string) => {
    // Try context translations first
    let result = t(translations, key);
    
    // Fallback to global window translations
    if (result === key && typeof window !== 'undefined' && window.translations) {
      result = t(window.translations, key);
    }
    
    return result;
  };

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <div className="flex-1">
          <h1 className="text-xl font-bold text-foreground mb-2">
            {title}
          </h1>
          {!isCollapsed && (
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="flex items-center gap-2 ml-4"
        >
          {isCollapsed ? (
            <>
              <ChevronDown className="w-4 h-4" />
              {getTranslation('components.collapsible_header.show_info')}
            </>
          ) : (
            <>
              <ChevronUp className="w-4 h-4" />
              {getTranslation('components.collapsible_header.hide_info')}
            </>
          )}
        </Button>
      </div>
      
      {!isCollapsed && (
        <div className="transition-all duration-300 ease-in-out">
          {children}
        </div>
      )}
    </div>
  );
}