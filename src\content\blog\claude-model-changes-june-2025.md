---
title: "Modell-Updates bei iteratec ab 12.06.2025: Claude 4 und OpenAI o4 Generation"
excerpt: "Ab dem 12. Juni 2025 führt iteratec umfassende Modell-Updates durch: Claude 3.5 Modelle werden durch Claude 4 Sonnet/Opus ersetzt, OpenAI o1/o3-mini werden durch die neue o3/o4-mini Generation abgelöst. Alle neuen Modelle stehen in iteraGPT und über die API zur Verfügung."
category: "release-notes"
tags: ["claude", "openai", "model-updates", "iteragpt", "api", "non-eu-models", "claude-4", "o4", "o4-mini"]
publishDate: "2025-06-11T15:30:00Z"
lastUpdated: "2025-06-11T15:30:00Z"
author:
  name: "iteratec AI Team"
  role: "Platform Engineering"
readingTime: 4
featured: true
relatedModelIds: ["claude-sonnet-4", "claude-opus-4", "o4-mini-2025-04-16", "o3-2025-04-16"]
releaseVersion: "2025.06.12"
changelog:
  - type: "removed"
    description: "Alle Claude 3.5 Modelle werden aus dem iteratec-Portfolio entfernt"
    impact: "major"
    technicalDetails: "Betrifft Claude 3.7 Sonnet, Claude 3.7 Sonnet v2 und Claude 3.5 Haiku - sowohl in iteraGPT als auch über die API"
  - type: "removed"
    description: "OpenAI o1 und o3-mini Modelle werden durch neuere Generationen ersetzt"
    impact: "major"
    technicalDetails: "o1 wird vollständig entfernt, o3-mini wird durch o4-mini ersetzt - bessere Performance bei gleichen Kosten"
  - type: "added"
    description: "Claude 4 Sonnet als EU-Modell verfügbar"
    impact: "major"
    technicalDetails: "Bereitstellung als offenes Modell ohne EU-Datenschutzrestriktionen, verfügbar in iteraGPT und über API"
  - type: "added"
    description: "Claude 4 Opus als Non-EU-Modell verfügbar (nur USA direkt über Anthropic)"
    impact: "major"
    technicalDetails: "Bereitstellung als offenes Modell ohne EU-Datenschutzrestriktionen, verfügbar in iteraGPT und über API"
  - type: "added"
    description: "OpenAI o4-mini als Nachfolger von o3-mini verfügbar"
    impact: "major"
    technicalDetails: "Verbesserte Reasoning-Performance bei gleichen Kosten, erweiterte Vision-Fähigkeiten und Web-Browsing-Support"
  - type: "added"
    description: "OpenAI o3 als neues Premium-Reasoning-Modell verfügbar"
    impact: "major"
    technicalDetails: "Ersetzt o1 mit deutlich verbesserter Performance in Mathematik, Coding und wissenschaftlichem Reasoning"
metaDescription: "Ab 12. Juni 2025 führt iteratec umfassende Modell-Updates durch: Claude 4 Sonnet/Opus ersetzen Claude 3.5, OpenAI o4/o4-mini lösen o1/o3-mini ab. Verfügbar in iteraGPT und über API."
metaKeywords: ["Claude 4", "OpenAI o4", "iteratec", "Non-EU Modelle", "AI Platform", "Enterprise AI", "Reasoning Models"]
featuredImage: "/images/blog/2025-06-anthropic-claude.png"
---

Am **12. Juni 2025** führt iteratec umfassende Änderungen am KI-Modell-Portfolio durch. Diese strategische Neuausrichtung betrifft sowohl Anthropic Claude als auch OpenAI Modelle und bringt deutlich leistungsstärkere Reasoning-Fähigkeiten sowie erweiterte Verfügbarkeitsoptionen für unsere Enterprise-Kunden.

## Die wichtigsten Änderungen im Überblick

### ❌ Entfernung veralteter Modelle

**Claude 3.5 Modelle** werden vollständig aus dem iteratec-Portfolio entfernt:
**OpenAI Modelle** der vorherigen Generation werden ersetzt (**o1** wird vollständig entfernt, **o3-mini** wird durch **o4-mini** ersetzt)

Diese Änderungen betreffen sowohl die iteraGPT-Benutzeroberfläche als auch den direkten API-Zugang.

### ✅ Neue Modelle der nächsten Generation

| Modell | Verfügbarkeit | Leistung | Nachfolger von |
|--------|---------------|----------|----------------|
| **Claude 4 Sonnet** | Intern: GCP (EU-Hosting)<br/>Offen: Anthropic (US-Hosting) | Deutlich verbesserte Reasoning-Fähigkeiten<br/>72.7% SWE-bench<br/>76.3% AIME 2024 | Claude 3.5/3.7 Sonnet |
| **Claude 4 Opus** | Offen: Anthropic (US-Hosting) | Sehr gute Leistungen für komplexe Aufgaben<br/>Premium-Performance in allen Bereichen | Claude 3.5 Opus |
| **o4-mini** | Intern: Azure (EU-Hosting)<br/>Offen: OpenAI (US-Hosting) | 93.4% AIME 2024<br/>68.1% SWE-bench<br/>Vision-Support, Web-Browsing<br/>$1.10/$4.40 per 1M Tokens | o3-mini |
| **o3** | Intern: Azure (EU-Hosting)<br/>Offen: OpenAI (US-Hosting) | 91.6% AIME 2024<br/>69.1% SWE-bench<br/>Sehr starkes Reasoning mit 2M Reasoning-Tokens<br/>Stark auch in Mathematik, wissenschaftliches Reasoning | o1 (vollständiger Ersatz) |

**Wichtige Verbesserungen:**
- **Claude 4**: 65% weniger Abkürzungen bei Aufgabenlösung, persistenter Speicher, MCP-Integration
- **o4-mini**: Neue Vision-Fähigkeiten (79.7% MMMU, 84.3% MathVista), gleiche Kosten wie Vorgänger


## Besonderheiten von Claude 4 im Vergleich zu Claude 3.7 Sonnet

**Claude 4** (insbesondere Sonnet 4 und Opus 4) bringt im Vergleich zu **Claude 3.7 Sonnet** zahlreiche Verbesserungen und neue Funktionen, die sich sowohl auf die Leistungsfähigkeit als auch auf die praktische Anwendbarkeit auswirken.

### Verbesserte Denk- und Problemlösefähigkeiten

- **Sorgfältigeres und schrittweises Denken:** Claude 4-Modelle vermeiden Abkürzungen oder Tricks bei der Aufgabenlösung deutlich besser – die Wahrscheinlichkeit hierfür ist um 65 % geringer als bei Sonnet 3.7[1][4][7].
- **Komplexere Aufgaben:** Sonnet 4 kann deutlich komplexere Anweisungen und Aufgaben umsetzen, insbesondere im Bereich Coding und Reasoning[4][5][7].
- **Erweiterte Denkschritte:** Beide Modelle (Sonnet 4 und Opus 4) sind in der Lage, innezuhalten und mehrere Schritte zu bedenken, bevor sie antworten – besonders wertvoll für mehrstufige Prozesse oder strukturierte Inhalte[1][5].

### Leistungssteigerung im Coding-Bereich

- **Benchmark-Ergebnisse:** Sonnet 4 erreicht im SWE-bench eine Genauigkeit von 72,7 %, während Sonnet 3.7 bei 62,3 % lag[2][7]. Dies ist ein signifikanter Sprung und macht sich besonders bei Softwareentwicklung, Debugging und Automatisierung bemerkbar.
- **Bessere Code-Navigation und Problemlösung:** Laut Partnern wie GitHub und Sourcegraph zeigt Sonnet 4 eine deutlich verbesserte Fähigkeit zur Code-Navigation und Ausführung komplexer Anweisungen[7].

### Neue und verbesserte Funktionen

- **Persistenter Speicher:** Claude 4 (insbesondere Opus 4) kann persistente Speicherdateien anlegen, wenn Entwickler Zugriff auf lokale Dateien erlauben. So kann das Modell wichtige Details über mehrere Sitzungen hinweg speichern und wiederverwenden[1][4][5].
- **Tool-Integration:** Claude 4 kann über das Model Context Protocol (MCP) mit APIs und Dateisystemen interagieren und externe Tools als Teil eines Workflows nutzen[1][7].
- **Files API und Prompt-Caching:** Neue API-Funktionen ermöglichen das Hochladen und Referenzieren von Dokumenten über mehrere Sitzungen hinweg sowie ein deutlich längeres Prompt-Caching (bis zu einer Stunde)[7].
- **Code-Ausführungswerkzeug:** Python-Code kann in einer abgeschotteten Umgebung ausgeführt werden, inklusive Datenanalyse und Visualisierung in einem Schritt[7].

### Verbesserte Kontextverarbeitung und Gedächtnis

- **Besseres Kontextverständnis:** Sonnet 4 baut auf der soliden Sprachverarbeitung von Sonnet 3.7 auf, bietet aber eine verbesserte kontextuelle Erinnerung und kann längere, komplexe Aufgaben besser verfolgen[8].
- **Thinking Summaries:** Für besonders lange Denkprozesse werden Zusammenfassungen erstellt, um die Kette von Gedankenschritten zu verdichten – nötig ist das aber nur in etwa 5 % der Fälle[7].

### Sicherheit und Zuverlässigkeit

- **Reduzierte Fehleranfälligkeit:** Die Modelle sind weniger anfällig für klassische Fehler wie das Übersehen von Details in Rätseln oder das falsche Zählen von Zeichen, was vorher durch explizite System-Prompts kompensiert werden musste[3].
- **Strengere Sicherheitsstandards:** Mit der Einführung der Claude 4-Modelle wurden auch strengere Sicherheitsstandards aktiviert[7].

### Zusammenfassung der wichtigsten Unterschiede

| Merkmal                      | Claude 3.7 Sonnet           | Claude 4 (Sonnet/Opus)           |
|------------------------------|-----------------------------|----------------------------------|
| Sorgfalt bei Aufgaben        | Gut                         | Deutlich verbessert, 65 % weniger Abkürzungen[1][4][7] |
| Coding-Leistung (SWE-bench)  | 62,3 %                      | 72,7 %[2][7]                     |
| Kontextgedächtnis            | Solide                      | Erweitert, persistente Speicher[1][4][5][8] |
| Tool-Integration             | Eingeschränkt               | MCP, Files API, Code-Ausführung[1][7] |
| Prompt-Caching               | 5 Minuten                   | Bis zu 1 Stunde[7]               |
| Anwendungsbereich            | Allzweck, Coding            | Komplexe, mehrstufige Aufgaben, Agenten-Workflows, Coding[6][7] |

**Fazit:**  
Claude 4 (Sonnet 4 und Opus 4) ist in nahezu allen Bereichen ein spürbares Upgrade gegenüber Claude 3.7 Sonnet: Es denkt sorgfältiger, löst komplexere Aufgaben, ist im Coding deutlich stärker und bringt neue Funktionen für Entwickler und Agenten-Workflows mit[1][4][5][6][7].

[1] https://www.ultralytics.com/de/blog/anthropics-claude-4-features-whats-new-and-improved
[2] https://www.datacamp.com/de/blog/claude-3-7-sonnet
[3] https://simonwillison.net/2025/May/25/claude-4-system-prompt/
[4] https://www.computerbase.de/news/apps/anthropic-claude-sonnet-4-und-claude-opus-4-freigegeben.92829/
[5] https://t3n.de/news/claude-4-neue-sonnet-opus-modell-anthropic-1689276/
[6] https://www.cometapi.com/de/claude-opus-4-vs-claude-sonnet-4-comparison/
[7] https://the-decoder.de/anthropic-stellt-claude-4-modelle-vor-und-aktiviert-strenge-sicherheitsstandards/
[8] https://www.edenai.co/post/claude-sonnet-3-7-vs-claude-sonnet-4
[9] https://kinews24.de/claude-3-7-sonnet/
[10] https://www.datacamp.com/de/blog/claude-4

## Leistungsverbesserungen im Detail auf Benchmark-Ebene

Die neuen Modelle bieten erhebliche Verbesserungen gegenüber ihren Vorgängern:

### Claude 4 Benchmark-Vergleich
- **SWE-bench Verified**: Claude 4 Sonnet erreicht 72.7% (vs. 62.3% bei Claude 3.7 Sonnet)
- **AIME 2024 (Mathematik)**: Claude 4 Sonnet erreicht 76.3% (vs. 54.8% bei Claude 3.7 Sonnet)
- **LiveCodeBench v2025**: Claude 4 Sonnet erreicht 70.9% (vs. 63.8% bei Claude 3.7 Sonnet)
- **Terminal-bench**: Claude 4 Sonnet erreicht 35.5% (vs. 35.2% bei Claude 3.7 Sonnet)
- **MMLU (Multilingual)**: Claude 4 Sonnet erreicht 86.5% (vs. 85.9% bei Claude 3.7 Sonnet)

### OpenAI o4 Generation Benchmark-Vergleich
- **AIME 2024**: o4-mini erreicht 93.4% (vs. 86.5% bei o3-mini), o3 erreicht 91.6% (vs. 74.3% bei o1)
- **SWE-bench Verified**: o4-mini erreicht 68.1% (vs. 48.0% bei o3-mini), o3 erreicht 69.1% (vs. 48.9% bei o1)
- **GPQA Diamond**: o4-mini erreicht 81.4% (vs. 75.0% bei o3-mini), o3 erreicht 83.3% (vs. 78.0% bei o1)
- **LiveCodeBench v2025**: o4-mini erreicht 68.3% (vs. 69.5% bei o3-mini), o3 erreicht 75.9%
- **Vision-Fähigkeiten**: o4-mini neu mit MMMU 79.7%, MathVista 84.3% (o3-mini hatte keine Vision)

## Vergleich: OpenAI o1 vs. o3

OpenAI o3 ist die direkte Weiterentwicklung von o1 und bringt in nahezu allen relevanten Bereichen deutliche Verbesserungen. Die wichtigsten Unterschiede und Besonderheiten lassen sich wie folgt zusammenfassen:

**Leistungsfähigkeit und Benchmarks**

- **Visuelles und logisches Denken:**  
  o3 übertrifft o1 in anspruchsvollen Benchmarks wie dem MMMU College-level Visual Problem-Solving (82,9 % vs. 77,6 %), MathVista Visual Math Reasoning (86,8 % vs. 71,8 %) und CharXiv-Reasoning Scientific Figure Reasoning (78,6 % vs. o1)[1][2].

- **Mathematische und wissenschaftliche Aufgaben:**  
  o3 erzielt beim AIME 2024 eine Genauigkeit von 91,6 % (o1: 74,3 %) und beim GPQA Diamond (PhD-Level Wissenschaftsfragen) 83,3 % (o1: 78 %)[1][2][4].

- **Coding und Softwareentwicklung:**  
  o3 erreicht 69,1 % Genauigkeit im SWE-Bench Verified Software Engineering Benchmark (o1: 48,9 %) und einen ELO-Wert von 2706 im Competitive Programming (o1: 1891)[1][2]. Auch bei Code-Editing-Benchmarks liegt o3 deutlich vorn[5].

**Praktische Unterschiede und Funktionen**

| Merkmal                      | OpenAI o1                  | OpenAI o3                      |
|------------------------------|----------------------------|--------------------------------|
| Geschwindigkeit              | Solide, aber langsamer     | Deutlich schneller (z. B. o3-mini: 24 % schneller als o1-mini)[6][4] |
| Genauigkeit & Zuverlässigkeit| Gut für einfache Aufgaben  | Höhere Genauigkeit, auch bei komplexen Problemen (z. B. 88 % im ARC-AGI Benchmark vs. 32 % bei o1)[4] |
| Tiefe des Verständnisses     | Oberflächliche Checks      | Erkennt auch subtile Fehler, z. B. in Code-Reviews[5] |
| Anpassungsfähigkeit          | Eingeschränkt              | Vielseitig, für komplexe und verschiedene Aufgaben geeignet[4] |
| Generative Fähigkeiten       | Eingeschränkt              | Hochwertige Inhalte, komplexe Problemlösung[4] |
| Sicherheit                   | Hoher Standard             | Neue Sicherheitsmechanismen, deliberative alignment[6] |
| Tool-Kompatibilität          | Begrenzt                   | Breite Unterstützung von Tools und Plattformen[4] |

**Zusammengefasst:**
- **o3** ist in allen Benchmarks, insbesondere beim logischen Denken, Coding und bei mathematisch-wissenschaftlichen Aufgaben, seinem Vorgänger **o1** deutlich überlegen[1][2][4][5].
- **o3** erkennt in der Praxis auch subtilere Fehler und Probleme, etwa in Code-Reviews oder bei komplexen Aufgabenstellungen, während o1 eher auf oberflächliche Checks beschränkt bleibt[5].
- **o3** ist schneller, vielseitiger und bietet fortschrittlichere Sicherheits- und Anpassungsmechanismen[4][6].

**Fazit:**  
OpenAI o3 ist für fortgeschrittene, komplexe und professionelle Anwendungen die klare Wahl, während o1 weiterhin eine solide Option für einfachere, kostengünstige Aufgaben bleibt.

[1] https://www.datacamp.com/blog/o3-openai
[2] https://www.datacamp.com/de/blog/o3-openai
[3] https://zapier.com/blog/openai-o1/
[4] https://www.hixx.ai/de/blog/awesome-ai-tools/open-ai-03
[5] https://www.codeant.ai/blogs/o1-vs-o3-mini-ai-code-review-comparison
[6] https://openai.com/index/openai-o3-mini/
[7] https://www.byteplus.com/en/topic/516725
[8] https://www.helicone.ai/blog/openai-o3
[9] https://community.openai.com/t/o1-model-is-better-than-o3/1240304
[10] https://www.reddit.com/r/OpenAI/comments/1iemnvi/openai_o3mini/?tl=de