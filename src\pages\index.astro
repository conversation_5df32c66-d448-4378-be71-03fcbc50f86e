---
import LanguageSelector from "../components/LanguageSelector.astro";
import { getCurrentLanguage } from "../utils/i18n";

const currentLang = getCurrentLanguage();
---

<!doctype html>
<html lang={currentLang}>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Weiterleitung...</title>
    <!-- Sofortige Meta-Refresh Weiterleitung -->
    <meta http-equiv="refresh" content="0; url=/models/" />
    <!-- Fallback JavaScript-Redirect -->
    <script>
      window.location.replace("/models/");
    </script>
    <style>
      body {
        margin: 0;
        padding: 0;
        background-color: #f8fafc;
        font-family:
          system-ui,
          -apple-system,
          sans-serif;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100vh;
        opacity: 0;
        transition: opacity 0.1s ease-in-out;
      }
      .redirect-message {
        text-align: center;
        color: #64748b;
      }
      .spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid #e2e8f0;
        border-radius: 50%;
        border-top-color: #3b82f6;
        animation: spin 1s ease-in-out infinite;
        margin-bottom: 16px;
      }
      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <LanguageSelector currentLang={currentLang} />
    <div class="redirect-message">
      <div class="spinner"></div>
      <p data-i18n="redirect">Weiterleitung zur Modell-Übersicht...</p>
    </div>
  </body>
</html>

<script>
  import i18next from "../utils/i18n";

  // Initialize translations
  document.addEventListener("DOMContentLoaded", () => {
    const elements = document.querySelectorAll("[data-i18n]");
    elements.forEach((element) => {
      const key = element.getAttribute("data-i18n");
      if (key) {
        element.textContent = i18next.t(key);
      }
    });
  });
</script>
