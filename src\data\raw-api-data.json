{"data": [{"id": "52e4bcbd1fa5f2127c714f1d60cd7c5c0aa962254312ccc51aad79049a2780ce", "name": "gcp/mistral-large-2411", "provider": "vertex_ai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "maxTokens": null, "maxInputTokens": null, "maxOutputTokens": null, "inputCostPerToken": 2e-06, "outputCostPerToken": 6e-06, "supportsFunctionCalling": false, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": false, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": false, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "gcp/mistral-large-2411", "contextWindow": null}, {"id": "1384597a6446d53ec9618bb6b4cb66e6dce29601166c128e63f4f4001de0a7d4", "name": "azure/gpt-4o-mini", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "maxTokens": 16384, "maxInputTokens": 128000, "maxOutputTokens": 16384, "inputCostPerToken": 1.65e-07, "outputCostPerToken": 6.6e-07, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "azure/gpt-4o-mini", "contextWindow": 128000}, {"id": "4b5c29236cf07fcc922a7de6bc8a3bae2631253b5115c664f59dbee2574ccc12", "name": "aws/claude-3.5-sonnet", "provider": "bedrock", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "maxTokens": 4096, "maxInputTokens": 200000, "maxOutputTokens": 4096, "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "aws/claude-3.5-sonnet", "contextWindow": 200000}, {"id": "a8b22052005b11412b35b2b08bf7d384fe1c234745c92b29eefbd8aab7227b8a", "name": "gcp/gemini-2.5-flash-preview-05-20", "provider": "vertex_ai-language-models", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "maxTokens": 65535, "maxInputTokens": 1048576, "maxOutputTokens": 65535, "inputCostPerToken": 1.5e-07, "outputCostPerToken": 6e-07, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": false, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": true, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "key": "gcp/gemini-2.5-flash-preview-05-20", "contextWindow": 1048576}, {"id": "4067cbebbc6310ab092389d3290528466b55072a473f5cb8a70ef8ee3b0e1dd6", "name": "azure/o3", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "maxTokens": 100000, "maxInputTokens": 200000, "maxOutputTokens": 100000, "inputCostPerToken": 1e-05, "outputCostPerToken": 4e-05, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "key": "azure/o3", "contextWindow": 200000}, {"id": "52de540253c915ae29abbe8fa51f4800a94f4a4bc44ea9c1445fec21c26359d8", "name": "dall-e-3", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "image_generation", "maxTokens": null, "maxInputTokens": null, "maxOutputTokens": null, "inputCostPerToken": 0, "outputCostPerToken": 0, "supportsFunctionCalling": false, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": false, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": false, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "dall-e-3", "contextWindow": null}, {"id": "cca88e0711911265f786e93308c05b9bd5b0f2a1dd07498e08decdc396a4bb54", "name": "iteratec/Qwen2.5-Coder-32B-Instruct", "provider": "hosted_vllm", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "confidential", "mode": "chat", "maxTokens": 32768, "maxInputTokens": 32768, "maxOutputTokens": 8192, "inputCostPerToken": 0, "outputCostPerToken": 0, "supportsFunctionCalling": false, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": false, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": false, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "iteratec/Qwen2.5-Coder-32B-Instruct", "contextWindow": 32768}, {"id": "e14b8f46a4eef996a60b684b01d32ab58723daece0788871dcf046ecc2b2b192", "name": "gcp/codestral-2501", "provider": "vertex_ai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "completion", "maxTokens": null, "maxInputTokens": null, "maxOutputTokens": null, "inputCostPerToken": 2e-07, "outputCostPerToken": 6e-07, "supportsFunctionCalling": false, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": false, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": false, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "gcp/codestral-2501", "contextWindow": null}, {"id": "116d636e3b6c757464290e4764697b56537fb6122c7d0bdd19063335264832ac", "name": "aws/claude-3.7-sonnet", "provider": "bedrock_converse", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "maxTokens": 8192, "maxInputTokens": 200000, "maxOutputTokens": 8192, "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "key": "aws/claude-3.7-sonnet", "contextWindow": 200000}, {"id": "f405328a228f46906a4e02d35a4b21a60752bb5b6e841617c952f035515efc46", "name": "azure/DeepSeek-R1", "provider": "azure_ai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "maxTokens": 8192, "maxInputTokens": 128000, "maxOutputTokens": 8192, "inputCostPerToken": 1.35e-06, "outputCostPerToken": 5.4e-06, "supportsFunctionCalling": false, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": false, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "key": "azure/DeepSeek-R1", "contextWindow": 128000}, {"id": "b3cb6e3163d6a857deba4eb73921eade14bcd7506c5744a7cec6133b0c4eea9f", "name": "gcp/gemini-2.5-pro-preview-05-06", "provider": "vertex_ai-language-models", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "maxTokens": 65535, "maxInputTokens": 1048576, "maxOutputTokens": 65535, "inputCostPerToken": 1.25e-06, "outputCostPerToken": 1e-05, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": false, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": true, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "key": "gcp/gemini-2.5-pro-preview-05-06", "contextWindow": 1048576}, {"id": "c613d56ff09282d3bf42d8db5e096aabb2cb1af195c252318c9900b5c15f3881", "name": "anthropic/claude-opus-4", "provider": "anthropic", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "maxTokens": 32000, "maxInputTokens": 200000, "maxOutputTokens": 32000, "inputCostPerToken": 1.5e-05, "outputCostPerToken": 7.5e-05, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "key": "anthropic/claude-opus-4", "contextWindow": 200000}, {"id": "73ec4cb691af7b141b3d42de3a3a94cf85ad95ebf15900bd395ac4a31cf8e9f5", "name": "openai/gpt-4o-mini", "provider": "openai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "maxTokens": 16384, "maxInputTokens": 128000, "maxOutputTokens": 16384, "inputCostPerToken": 1.5e-07, "outputCostPerToken": 6e-07, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "openai/gpt-4o-mini", "contextWindow": 128000}, {"id": "45d635b4878e44c81846b2c40abdf02cb5f86a58a95fe8e269c1ca8c5ffbea07", "name": "gcp/claude-3.7-sonnet", "provider": "vertex_ai-anthropic_models", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "maxTokens": 8192, "maxInputTokens": 200000, "maxOutputTokens": 8192, "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "key": "gcp/claude-3.7-sonnet", "contextWindow": 200000}, {"id": "ac724506793c23497031cb162cb250246e935e2714ac61dab66d44674f967e8a", "name": "openai/gpt-4o", "provider": "openai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "maxTokens": 16384, "maxInputTokens": 128000, "maxOutputTokens": 16384, "inputCostPerToken": 2.5e-06, "outputCostPerToken": 1e-05, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "openai/gpt-4o", "contextWindow": 128000}, {"id": "e083bf210e7b7927ecb9777966c7f4353b3c75f00e6100e959be9e9d2b24a87b", "name": "azure/gpt-4.1-nano", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "maxTokens": 32768, "maxInputTokens": 1047576, "maxOutputTokens": 32768, "inputCostPerToken": 1e-07, "outputCostPerToken": 4e-07, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": true, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "azure/gpt-4.1-nano", "contextWindow": 1047576}, {"id": "df445e425271aba43b23e6f57a74c0325f8cac1d42c7884cf97677204882d60f", "name": "azure/gpt-4.1", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "maxTokens": 32768, "maxInputTokens": 1047576, "maxOutputTokens": 32768, "inputCostPerToken": 2e-06, "outputCostPerToken": 8e-06, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": true, "supportsNativeStreaming": true, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "azure/gpt-4.1", "contextWindow": 1047576}, {"id": "97fc12f52e6d7c6ee714a3d7233f8d31ae779d6c9f750754d0853789e2b21867", "name": "gcp/claude-sonnet-4", "provider": "vertex_ai-anthropic_models", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "maxTokens": 64000, "maxInputTokens": 200000, "maxOutputTokens": 64000, "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "key": "gcp/claude-sonnet-4", "contextWindow": 200000}, {"id": "3572924e258b8d6a90631d41b3db2d1b9d3a2be520e09a50e99243c9e0f3b5d0", "name": "gcp/gemini-2.0-flash", "provider": "vertex_ai-language-models", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "maxTokens": 8192, "maxInputTokens": 1048576, "maxOutputTokens": 8192, "inputCostPerToken": 1.5e-07, "outputCostPerToken": 6e-07, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": false, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": true, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": true, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "gcp/gemini-2.0-flash", "contextWindow": 1048576}, {"id": "b78f22528436c67ef5b42f79f66aa1ea29119ff82b54f2a27befaacea8dfcd4c", "name": "gcp/claude-3.5-sonnet", "provider": "vertex_ai-anthropic_models", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "maxTokens": 8192, "maxInputTokens": 200000, "maxOutputTokens": 8192, "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "gcp/claude-3.5-sonnet", "contextWindow": 200000}, {"id": "bc323220bd2abd5c265f5a3a7ac229bd82b24a3c8a64c508dfd8e14a1f68d425", "name": "azure/text-embedding-ada-002", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "embedding", "maxTokens": 8191, "maxInputTokens": 8191, "maxOutputTokens": null, "inputCostPerToken": 1e-07, "outputCostPerToken": 0, "supportsFunctionCalling": false, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": false, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": false, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "azure/text-embedding-ada-002", "contextWindow": 8191}, {"id": "9b16155c998c0b236dfe1e26efb2a9e31fc333c97abfb7364db3a4c1255eb03b", "name": "openai/o4-mini", "provider": "openai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "maxTokens": 100000, "maxInputTokens": 200000, "maxOutputTokens": 100000, "inputCostPerToken": 1.1e-06, "outputCostPerToken": 4.4e-06, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": false, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "key": "openai/o4-mini", "contextWindow": 200000}, {"id": "0822ca65041b4874826203897edfe68f15cb7fb668f8a502ce04068f4d67ec21", "name": "anthropic/claude-sonnet-4", "provider": "anthropic", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "maxTokens": 64000, "maxInputTokens": 200000, "maxOutputTokens": 64000, "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "key": "anthropic/claude-sonnet-4", "contextWindow": 200000}, {"id": "5e909417b7d202166fb9e4dbe331fc5564b88b714a49f9ffba9d42c3c2a114b4", "name": "azure/o4-mini", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "maxTokens": 100000, "maxInputTokens": 200000, "maxOutputTokens": 100000, "inputCostPerToken": 1.1e-06, "outputCostPerToken": 4.4e-06, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "key": "azure/o4-mini", "contextWindow": 200000}, {"id": "891b4272b391dbb580fa154d997e7458627f93a3a609e1dbb933b1c7909ec1fb", "name": "openai/gpt-4.1", "provider": "openai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "maxTokens": 32768, "maxInputTokens": 1047576, "maxOutputTokens": 32768, "inputCostPerToken": 2e-06, "outputCostPerToken": 8e-06, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": true, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "openai/gpt-4.1", "contextWindow": 1047576}, {"id": "7e121354d7b80dd7199b648b59529ca3f1f86683a84490839da99fd223ac8cd9", "name": "anthropic/claude-3.7-sonnet", "provider": "anthropic", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "maxTokens": 128000, "maxInputTokens": 200000, "maxOutputTokens": 128000, "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": true, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "key": "anthropic/claude-3.7-sonnet", "contextWindow": 200000}, {"id": "a71d34a1a02f35df7fe78cc81817d993bb41333cc3429ebed5ab40fda829baca", "name": "azure/gpt-4o", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "maxTokens": 16384, "maxInputTokens": 128000, "maxOutputTokens": 16384, "inputCostPerToken": 2.5e-06, "outputCostPerToken": 1e-05, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "azure/gpt-4o", "contextWindow": 128000}, {"id": "0a77ff834ac00b7ad0fbfa5d8e7f91da255ef30e27c91dbb40c9a4014d6369c2", "name": "gcp/claude-3.5-sonnet-v2", "provider": "vertex_ai-anthropic_models", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "maxTokens": 8192, "maxInputTokens": 200000, "maxOutputTokens": 8192, "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "gcp/claude-3.5-sonnet-v2", "contextWindow": 200000}, {"id": "23f2c965faa5bde3700b55bad15992c65e89e8c203de01ada55ad7265d8012c8", "name": "openai/o3", "provider": "openai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "maxTokens": 100000, "maxInputTokens": 200000, "maxOutputTokens": 100000, "inputCostPerToken": 1.5e-05, "outputCostPerToken": 6e-05, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "key": "openai/o3", "contextWindow": 200000}, {"id": "3bc4cd707844c1be43142845e5b0c732006765cc8c661c6c529b7addd4b053eb", "name": "iteratec/bge-m3", "provider": "openai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "confidential", "mode": "embedding", "maxTokens": 8192, "maxInputTokens": 8192, "maxOutputTokens": null, "inputCostPerToken": 0, "outputCostPerToken": 0, "supportsFunctionCalling": false, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": false, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": false, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "key": "iteratec/bge-m3", "contextWindow": 8192}], "status": 200, "message": "Success", "timestamp": "2025-06-21T08:40:19.091Z", "source": "api", "apiConfig": {"baseUrl": "https://api.iteragpt.iteratec.de", "useMockData": false}}