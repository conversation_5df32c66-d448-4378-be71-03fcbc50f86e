{"site": {"title": "<PERSON><PERSON>", "description": "LLM Browser - Vergleiche und bewerte Sprachmodelle"}, "nav": {"models": "Models", "blog": "Blog", "recommendations": "Empfehlungen", "benchmarks": "Benchmarks", "api_usage": "API-Nutzung@iteratec"}, "footer": {"copyright": "© 2025 LLM Browser - Vise-Coding", "built_with": "Erstellt mit Astro, React, TypeScript & Tailwind CSS"}, "language_selector": {"label": "Sprache wählen", "de": "De<PERSON>ch", "en": "<PERSON><PERSON><PERSON>", "pl": "Polnisch"}, "language": "<PERSON><PERSON><PERSON>", "redirect": "Weiterleitung zur Modell-Übersicht...", "meta_description": "LLM Browser - Vergleiche und bewerte Sprachmodelle", "vise_coding": "Vise-Coding", "loading": "Laden...", "models": {"header": "LL<PERSON> Modell-<PERSON><PERSON><PERSON>", "description": "Entdecke {{count}} KI-Modelle mit detaillierten Informationen zu Preisen, Fähigkeiten und Benchmark-Ergebnissen. Ziel ist es, die Auswahl geeigneter Modelle für die tägliche Arbeit interaktiver, einfacher und transparenter zu machen. Andernfalls müssen die Informationen aus vielen Quellen zusammengesucht werden. Basierend auf Live-Daten von iteraGPT (LiteLLM) und AI-generierten Model Cards aus verschiedenen Quellen.", "filters": {"all": "Alle", "security": "Sicherheit", "mode": "Modus", "chat": "Cha<PERSON>", "completion": "Completion", "embedding": "Embedding", "image_generation": "Bildgenerierung", "search_placeholder": "Suche nach Name, Anbieter oder Modellgruppe...", "of": "von", "models": "<PERSON><PERSON>"}, "stats": {"total_models": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "benchmarks": "Benchmarks", "average_score": "Durchschnittswert", "top_performer": "Top-Performer", "filtered_count": "{{filtered}} von {{total}} <PERSON>len"}, "comparison": {"title": "Modellvergleich ({{count}} Modelle)", "reset_selection": "Auswahl zurücksetzen", "property": "Eigenschaft", "provider": "<PERSON><PERSON><PERSON>", "litellm_availability": "LiteLLM Verfügbarkeit", "available": "Verfügbar", "model_card_only": "Nur Model Card", "context_window": "Kontext-Fenster", "max_output_tokens": "<PERSON> Output Tokens", "input_cost": "Input Kosten (pro 1M Tokens)", "output_cost": "Output Kosten (pro 1M Tokens)", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "capabilities": "Fähigkeiten", "supported_platforms": "Unterstützte Plattformen", "metric": "<PERSON><PERSON>", "range": "<PERSON><PERSON><PERSON>", "no_details_available": "<PERSON><PERSON> detail<PERSON>ten Informationen verfügbar.", "other_benchmarks": "Weitere Benchmarks", "at": "bei", "and": "und", "well_formed_code": "well formed code", "category": "<PERSON><PERSON><PERSON>", "difficulty": "Schwierigkeit", "variants": "<PERSON><PERSON><PERSON>", "not_available": "N/V", "aider_polyglot_short": "Aider-Polyglot (o)", "website": "Website", "paper": "Publikation", "aider_benchmark": {"title": "Aider's polyglot benchmark", "description": "<PERSON>t die Fähigkeit von Modellen, Code in verschiedenen Programmiersprachen zu bearbeiten und zu verbessern.", "metric": "Pass Rate (2nd attempt)", "range": "0-100%", "fallback_description": "Aider's coding benchmark - misst die Fähigkeit von Modellen, Code zu bearbeiten und zu verbessern."}}, "capabilities": {"vision": "Vision", "pdf_input": "PDF Input", "audio_input": "Audio Input", "audio_output": "Audio Output", "embedding_image": "Embedding Image", "function_calling": "Function Calling", "prompt_caching": "Prompt Caching", "reasoning": "Reasoning", "system_messages": "System Messages"}, "table": {"pagination": {"showing": "Zeige {{start}} bis {{end}} von {{total}} Modellen", "previous": "Zurück", "next": "<PERSON><PERSON>"}, "headers": {"select": "Auswählen", "security": "Sicherheit", "model_card": "Model Card", "litellm_status": "LiteLLM/Status", "name": "Name", "provider": "<PERSON><PERSON><PERSON>", "mode": "Modus", "context": "Kontext", "max_output": "Max Output", "input_cost_per_million": "Input Kosten/1M", "output_cost_per_million": "Output Kosten/1M", "polyglot_score": "Polyglot Score", "support": "Support", "details": "Details", "show_filters": "Filter anzeigen", "fullscreen": "Vollbild"}, "tooltips": {"confidential": "Confidential", "internal": "Internal", "public": "Public", "model_card_available": "Model Card verfügbar", "deprecated": "Deprecated", "shutdown_date": "Abschaltung: {{date}}", "litellm_available": "LiteLLM verfügbar", "model_card_only": "Nur Model Card", "chat": "Cha<PERSON>", "embedding": "Embedding", "image": "Image", "vision_processing": "Vision/Bildverarbeitung", "pdf_input": "PDF Input", "audio_input": "Audio Input", "audio_output": "Audio Output", "embedding_image_input": "Embedding Image Input", "function_calling": "Function Calling", "prompt_caching": "Prompt Caching", "reasoning": "Reasoning", "system_messages": "System Messages"}, "empty_state": "<PERSON><PERSON> gefunden", "select_model": "Select model {{name}}", "details_button": "Details"}}, "blog": {"title": "LLM Blog", "description": "Aktuelle Insights zu AI-Modellen, Release Notes und Benchmark-Analysen. Bleiben Sie auf dem Laufenden über die neuesten Entwicklungen in der LLM-Landschaft.", "sectionModelAnalysis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sectionModelAnalysisDesc": "Detaillierte Reviews der neuesten AI-Modelle", "sectionReleaseNotes": "Release Notes", "sectionReleaseNotesDesc": "Neueste Updates und Änderungen an Modellen", "sectionBenchmarkAnalysis": "Benchmark-<PERSON><PERSON><PERSON>", "sectionBenchmarkAnalysisDesc": "Tiefgehende Auswertungen von Performance-Tests", "sectionIndustryNews": "Branchen-News", "sectionIndustryNewsDesc": "Wichtige Entwicklungen im AI-Markt"}, "recommendations": {"title": "Empfehlungen für Unternehmen", "description": "Intelligentes Empfehlungssystem für {{totalModels}} LLM-<PERSON><PERSON> von {{totalProviders}} Anbietern. Basierend auf Standard-Anwendungsfällen in Unternehmen werden die optimalen Modelle für verschiedene Szenarien empfohlen. Berücksichtigt werden Benchmark-Performance, Capabilities, Kosten und weitere Faktoren für fundierte Entscheidungen.", "availableModels": "Verfügbare Modelle", "providers": "<PERSON><PERSON><PERSON>", "gaStatus": "GA Status", "avgInputCost": "⌀ Input Kosten", "avgOutputCost": "⌀ Output Kosten", "perMillion": "pro 1M Tokens"}, "benchmark": {"title": "Benchmark Ergebnisse", "description": "Detaillierte Analyse der Polyglot-Benchmark-Ergebnisse für {{totalBenchmarks}} Benchmark-Tests.", "testedModels": "Getestete Modelle", "averageScore": "Durchschnittsscore", "highestScore": "Höchster Score", "testCases": "Testfälle", "about": "Über den Polyglot Benchmark:", "aboutText1": "Dieser Benchmark basiert auf Exercism-Coding-Übungen und testet die Fähigkeit von Sprachmodellen, komplexe Programmierprobleme in 6 verschiedenen Sprachen zu lösen:", "languages": "C++, Go, Java, JavaScript, Python und Rust", "aboutText2": "Der Benchmark umfasst die {{hardest}} schwierigsten Übungen aus insgesamt {{total}} verfügbaren Exercism-Problemen und wurde entwickelt, um deutlich herausfordernder zu sein als frühere Benchmarks. Die Scores basieren auf der Anzahl erfolgreich gelöster Coding-Probleme und bieten eine präzise Bewertung der Code-Editing-Fähigkeiten moderner LLMs."}, "qc": {"title": "Benchmark-Vergleich für LLM-Modelle", "header": "Benchmark-Vergleich", "description": "Detaillierte Benchmark-Analyse für {{modelCount}} LLM-Modelle. Vergleiche die Performance verschiedener Modelle in {{benchmarkCount}} verschiedenen Benchmarks. Diese Übersicht ermöglicht einen direkten Vergleich der tatsächlichen Benchmark-Werte aus den Model Cards.", "availableBenchmarks": "Verfügbare Benchmarks", "avgBenchmarksPerModel": "⌀ Benchmarks/Modell", "mostCommonBenchmark": "Häufigster Benchmark", "modelsWithBenchmarks": "Modelle mit Benchmarks", "topBenchmarks": "Top 5 Benchmarks (nach Verfügbarkeit)", "models": "<PERSON><PERSON>"}, "components": {"collapsible_header": {"show_info": "Info anzeigen", "hide_info": "Info ausblenden"}}}