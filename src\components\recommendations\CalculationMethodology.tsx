import * as React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { Button } from "../ui/button";
import { Calculator } from "lucide-react";

interface CalculationMethodologyProps {
  trigger?: React.ReactNode;
}

export const CalculationMethodology: React.FC<CalculationMethodologyProps> = ({
  trigger,
}) => {
  const defaultTrigger = (
    <Button variant="outline" size="sm" className="gap-2">
      <Calculator className="w-4 h-4" />
      Berechnungsmethodik
    </Button>
  );

  return (
    <Dialog>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-foreground flex items-center gap-2">
            <Calculator className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            Berechnungsmethodik der Empfehlungen
          </DialogTitle>
          <DialogDescription>
            Detaillierte Erklärung des intelligenten Empfehlungssystems und der
            verwendeten Algorithmen
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 mt-6">
          {/* Übersicht des Scoring-Algorithmus */}
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center">
              <span className="text-2xl mr-2">🎯</span>
              Scoring-Algorithmus Übersicht
            </h3>
            <p className="text-muted-foreground mb-4">
              Unser intelligentes Empfehlungssystem bewertet jedes Modell für
              jeden Use Case anhand eines 100-Punkte-Systems. Der Gesamtscore
              setzt sich aus fünf gewichteten Faktoren zusammen:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  60%
                </div>
                <div className="text-sm text-muted-foreground">
                  Benchmark Performance
                </div>
              </div>
              <div className="text-center p-4 bg-green-50 dark:bg-green-900/30 rounded-lg">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  15%
                </div>
                <div className="text-sm text-muted-foreground">
                  Erforderliche Capabilities
                </div>
              </div>
              <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/30 rounded-lg">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  10%
                </div>
                <div className="text-sm text-muted-foreground">
                  Kosten-Effizienz
                </div>
              </div>
              <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/30 rounded-lg">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  10%
                </div>
                <div className="text-sm text-muted-foreground">
                  Latenz/Geschwindigkeit
                </div>
              </div>
              <div className="text-center p-4 bg-teal-50 dark:bg-teal-900/30 rounded-lg">
                <div className="text-2xl font-bold text-teal-600 dark:text-teal-400">
                  5%
                </div>
                <div className="text-sm text-muted-foreground">
                  Verfügbarkeit
                </div>
              </div>
            </div>
          </div>

          {/* Benchmark-Performance Berechnung */}
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center">
              <span className="text-2xl mr-2">📊</span>
              Benchmark-Performance (45% Gewichtung)
            </h3>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                Benchmark-Scores werden intelligent normalisiert und
                Use-Case-spezifisch gewichtet:
              </p>

              <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  🎯 Kritische Coding-Benchmarks (1.5x Gewichtung):
                </h4>
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  • SWE-bench Verified • LiveCodeBench v2025 • Aider-Polyglot •
                  WebDev-Arena • Terminal-bench
                </p>
              </div>

              <div className="bg-purple-50 dark:bg-purple-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-purple-900 dark:text-purple-100 mb-2">
                  📏 Score-Normalisierung:
                </h4>
                <div className="text-sm text-purple-800 dark:text-purple-200 space-y-1">
                  <div>
                    <strong>Arena Scores:</strong> 1300=60pts, 1350=70pts,
                    1400=85pts, 1450+=95pts
                  </div>
                  <div>
                    <strong>ELO Ratings:</strong> 1200=30pts, 1800=70pts,
                    2400+=90pts
                  </div>
                  <div>
                    <strong>Standard Benchmarks:</strong> Direkte 0-100%
                    Übernahme
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Weitere Bewertungsfaktoren */}
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center">
              <span className="text-2xl mr-2">⚙️</span>
              Weitere Bewertungsfaktoren
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
                  ⚙️ Capabilities (25%)
                </h4>
                <p className="text-sm text-green-800 dark:text-green-200">
                  Proporional: (erfüllte Capabilities / benötigte Capabilities)
                  × 100
                </p>
              </div>
              <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  💰 Kosten-Score (10%)
                </h4>
                <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <div>
                    <strong>Budget (0-1$):</strong> 85-95 Punkte
                  </div>
                  <div>
                    <strong>Standard (1-5$):</strong> 70-85 Punkte
                  </div>
                  <div>
                    <strong>Premium (5$+):</strong> 50-70 Punkte
                  </div>
                </div>
              </div>
              <div className="bg-purple-50 dark:bg-purple-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-purple-900 dark:text-purple-100 mb-2">
                  ⚡ Latenz-Score (15%)
                </h4>
                <div className="text-sm text-purple-800 dark:text-purple-200 space-y-1">
                  <div>
                    <strong>Fastest:</strong> 100pts
                  </div>
                  <div>
                    <strong>Fast:</strong> 85pts
                  </div>
                  <div>
                    <strong>Moderately Fast:</strong> 70pts
                  </div>
                  <div>
                    <strong>Slow/Slowest:</strong> 50/30pts
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div className="bg-teal-50 dark:bg-teal-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-teal-900 dark:text-teal-100 mb-2">
                  📄 Context Window Score
                </h4>
                <div className="text-sm text-teal-800 dark:text-teal-200 space-y-1">
                  <div>
                    <strong>500k+:</strong> 120pts • <strong>200k+:</strong>{" "}
                    100pts • <strong>128k+:</strong> 90pts •{" "}
                    <strong>64k+:</strong> 80pts
                  </div>
                  <div>
                    <strong>32k+:</strong> 70pts • <strong>16k+:</strong> 60pts
                    • <strong>&lt;16k:</strong> 40pts
                  </div>
                </div>
              </div>
              <div className="bg-indigo-50 dark:bg-indigo-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-indigo-900 dark:text-indigo-100 mb-2">
                  🏢 Verfügbarkeit-Score (5%)
                </h4>
                <div className="text-sm text-indigo-800 dark:text-indigo-200 space-y-1">
                  <div>
                    <strong>GA:</strong> 100pts • <strong>Preview:</strong>{" "}
                    80pts • <strong>Andere:</strong> 60pts
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Qualitätsfaktoren & Coding-Optimierungen */}
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center">
              <span className="text-2xl mr-2">🔧</span>
              Qualitätsfaktoren & Coding-Optimierungen (2025)
            </h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-red-50 dark:bg-red-900/30 p-4 rounded-lg">
                  <h4 className="font-medium text-red-900 dark:text-red-100 mb-2">
                    ❌ Benchmark-Penalties
                  </h4>
                  <p className="text-sm text-red-800 dark:text-red-200">
                    -5 Punkte pro Benchmark mit &lt;40% in Factuality/Knowledge
                    Kategorien
                  </p>
                </div>
                <div className="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg">
                  <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
                    🎯 Multimodal-Bonus
                  </h4>
                  <p className="text-sm text-green-800 dark:text-green-200">
                    +5 Punkte für Vision-Capabilities bei
                    Data-Analysis/Documentation Use Cases
                  </p>
                </div>
              </div>

              <div className="bg-indigo-50 dark:bg-indigo-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-indigo-900 dark:text-indigo-100 mb-2">
                  💻 Spezielle Coding-Anpassungen
                </h4>
                <div className="text-sm text-indigo-800 dark:text-indigo-200 space-y-2">
                  <div>
                    <strong>Coding Use Cases:</strong> +10% zusätzliche
                    Benchmark-Gewichtung
                  </div>
                  <div>
                    <strong>Context Bonus:</strong> +3 Punkte für &gt;500k/200k,
                    +2 Punkte für &gt;128k Context Window
                  </div>
                  <div>
                    <strong>Betroffene Use Cases:</strong> code-generation,
                    code-review, debugging, refactoring, testing,
                    api-integration, devops-automation
                  </div>
                </div>
              </div>

              <div className="bg-amber-50 dark:bg-amber-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-amber-900 dark:text-amber-100 mb-2">
                  🔄 Finale Score-Berechnung
                </h4>
                <div className="text-sm text-amber-800 dark:text-amber-200">
                  <strong>Total Score = </strong>
                  (Benchmark × 45%) + (Capabilities × 25%) + (Cost × 10%) +
                  (Latency × 15%) + (Availability × 5%) + Multimodal-Bonus +
                  Coding-Anpassungen - Benchmark-Penalties
                </div>
              </div>
            </div>
          </div>

          {/* Use Case Benchmark-Mappings */}
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center">
              <span className="text-2xl mr-2">🗺️</span>
              Use Case Benchmark-Mappings
            </h3>
            <p className="text-muted-foreground mb-4">
              Jeder Use Case verwendet spezifische Benchmarks zur Bewertung. Die
              wichtigsten Code-Benchmarks werden priorisiert:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded">
                  <strong>
                    Code-generierung/-review/-debugging/-refactoring/-testing:
                  </strong>
                  <br />
                  SWE-bench Verified, LiveCodeBench v2025, Aider-Polyglot,
                  WebDev-Arena, Terminal-bench
                </div>
                <div className="bg-green-50 dark:bg-green-900/30 p-3 rounded">
                  <strong>API-Integration:</strong>
                  <br />+ ComplexFuncBench, TAU-bench Retail/Airline
                </div>
              </div>
              <div className="space-y-2">
                <div className="bg-purple-50 dark:bg-purple-900/30 p-3 rounded">
                  <strong>DevOps-Automatisierung:</strong>
                  <br />
                  Code-Benchmarks + TAU-bench Retail/Airline
                </div>
                <div className="bg-orange-50 dark:bg-orange-900/30 p-3 rounded">
                  <strong>Datenanalyse:</strong>
                  <br />
                  MATH, MMMU, MathVista, LiveCodeBench v2025
                </div>
                <div className="bg-teal-50 dark:bg-teal-900/30 p-3 rounded">
                  <strong>Lernen/Dokumentation:</strong>
                  <br />
                  MMLU, IFEval, MMLU, MultiChallenge
                </div>
              </div>
            </div>
          </div>

          {/* Bewertungsskala und Transparenz */}
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center">
              <span className="text-2xl mr-2">🏆</span>
              Bewertungsskala & Kategorisierung
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium text-foreground mb-3">
                  Suitability-Kategorien:
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>⭐ Ausgezeichnet</span>
                    <span className="font-bold text-green-600 dark:text-green-400">
                      ≥ 80 Punkte
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>✅ Gut</span>
                    <span className="font-bold text-blue-600 dark:text-blue-400">
                      65-79 Punkte
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>⚠️ Akzeptabel</span>
                    <span className="font-bold text-yellow-600 dark:text-yellow-400">
                      50-64 Punkte
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>❌ Begrenzt</span>
                    <span className="font-bold text-red-600 dark:text-red-400">
                      &lt; 50 Punkte
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-foreground mb-3">
                  Empfehlungskategorien:
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>🎯 Empfohlen</span>
                    <span className="font-bold text-green-600 dark:text-green-400">
                      ≥ 65 Punkte
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>🔄 Alternative</span>
                    <span className="font-bold text-yellow-600 dark:text-yellow-400">
                      50-64 Punkte
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>⛔ Nicht empfohlen</span>
                    <span className="font-bold text-red-600 dark:text-red-400">
                      &lt; 50 Punkte
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-foreground mb-3">
                  Kostenwirksamkeit:
                </h4>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div>
                    💰 <strong>Hoch:</strong> Budget + Score &gt;65
                  </div>
                  <div>
                    📊 <strong>Mittel:</strong> Standard + Score &gt;70
                  </div>
                  <div>
                    💎 <strong>Niedrig:</strong> Premium-Modelle
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Disclaimer */}
          <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-yellow-900 dark:text-yellow-100 mb-3 flex items-center">
              <span className="text-xl mr-2">⚠️</span>
              Wichtiger Hinweis
            </h3>
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              Diese Empfehlungen basieren auf algorithmischen Berechnungen und
              dienen als Orientierungshilfe. Für produktive Anwendungen sollten
              immer eigene Tests und Evaluationen durchgeführt werden. Preise
              und Verfügbarkeiten können sich ändern. Stand der Daten:{" "}
              {new Date().toLocaleDateString("de-DE")}.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
