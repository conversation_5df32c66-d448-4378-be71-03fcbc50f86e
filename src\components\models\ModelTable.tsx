import * as React from "react";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { <PERSON><PERSON> } from "../ui/button";
import { Card, CardContent } from "../ui/card";
import {
  Lock,
  LockKeyhole,
  Globe,
  MessageCircle,
  Package,
  Image,
  Eye,
  FileText,
  Headphones,
  Volume2,
  ImageIcon,
  Settings,
  HardDrive,
  Brain,
  MessageSquare,
  Maximize2,
  Minimize2,
  CreditCard,
  AlertTriangle,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import type { EnrichedModelData, SortField } from "./types";

interface ModelTableProps {
  models: EnrichedModelData[];
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  sortField: SortField;
  sortDirection: "asc" | "desc";
  selectedModelIds: string[];
  averageScore: number;
  onSort: (field: SortField) => void;
  onPageChange: (page: number) => void;
  onModelSelect: (model: EnrichedModelData) => void;
  onToggleSelection: (modelId: string) => void;
  formatCurrency: (value: number | null | undefined) => string;
  isFullscreen?: boolean;
  onToggleFullscreen?: () => void;
  getTranslation: (key: string, replacements?: Record<string, string | number>) => string;
}

export function ModelTable({
  models,
  currentPage,
  totalPages,
  itemsPerPage,
  sortField,
  sortDirection,
  selectedModelIds,
  averageScore,
  onSort,
  onPageChange,
  onModelSelect,
  onToggleSelection,
  formatCurrency,
  isFullscreen = false,
  onToggleFullscreen,
  getTranslation,
}: ModelTableProps) {
  // Pagination
  const paginatedModels = models.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <TooltipProvider>
      <Card className={`w-full ${isFullscreen ? 'max-w-none' : 'max-w-[1400px]'} mx-auto`}>
        <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table className="min-w-full">
            <TableCaption>
              <div className={`flex justify-between items-center ${isFullscreen ? 'p-3' : 'p-5'}`}>
                <div>
                  {getTranslation('models.table.pagination.showing', {
                    start: ((currentPage - 1) * itemsPerPage + 1).toString(),
                    end: Math.min(currentPage * itemsPerPage, models.length).toString(),
                    total: models.length.toString()
                  })}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(Math.max(currentPage - 1, 1))}
                    disabled={currentPage === 1}
                  >
                    {getTranslation('models.table.pagination.previous')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(Math.min(currentPage + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  >
                    {getTranslation('models.table.pagination.next')}
                  </Button>
                </div>
              </div>
            </TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <span className="sr-only">{getTranslation('models.table.headers.select')}</span>
                </TableHead>
                <TableHead className="w-10">
                  <span className="sr-only">{getTranslation('models.table.headers.security')}</span>
                </TableHead>
                <TableHead className="w-10">
                  <span className="sr-only">{getTranslation('models.table.headers.model_card')}</span>
                </TableHead>
                <TableHead className="w-10">
                  <span className="sr-only">{getTranslation('models.table.headers.litellm_status')}</span>
                </TableHead>
                <TableHead className="cursor-pointer min-w-[200px]" onClick={() => onSort("name")}>
                  {getTranslation('models.table.headers.name')} {sortField === "name" && (sortDirection === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead className="cursor-pointer min-w-[120px]" onClick={() => onSort("provider")}>
                  {getTranslation('models.table.headers.provider')} {sortField === "provider" && (sortDirection === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead className="w-10">
                  <span className="sr-only">{getTranslation('models.table.headers.mode')}</span>
                </TableHead>
                <TableHead className="cursor-pointer min-w-[100px]" onClick={() => onSort("contextWindow")}>
                  {getTranslation('models.table.headers.context')} {sortField === "contextWindow" && (sortDirection === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead className="cursor-pointer min-w-[120px]" onClick={() => onSort("maxOutputTokens")}>
                  {getTranslation('models.table.headers.max_output')} {sortField === "maxOutputTokens" && (sortDirection === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead className="cursor-pointer min-w-[120px]" onClick={() => onSort("inputCostPerToken")}>
                  {getTranslation('models.table.headers.input_cost_per_million')} {sortField === "inputCostPerToken" && (sortDirection === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead className="cursor-pointer min-w-[120px]" onClick={() => onSort("outputCostPerToken")}>
                  {getTranslation('models.table.headers.output_cost_per_million')} {sortField === "outputCostPerToken" && (sortDirection === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead className="cursor-pointer min-w-[110px]" onClick={() => onSort("benchmarkScore")}>
                  {getTranslation('models.table.headers.polyglot_score')} {sortField === "benchmarkScore" && (sortDirection === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead className="w-20">
                  <span className="text-xs">{getTranslation('models.table.headers.support')}</span>
                </TableHead>
                <TableHead className="text-right min-w-[80px]">
                  <div className="flex items-center justify-end gap-2">
                    <span>{getTranslation('models.table.headers.details')}</span>
                    {onToggleFullscreen && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={onToggleFullscreen}
                        className="h-6 w-6 p-0"
                        title={isFullscreen ? getTranslation('models.table.headers.show_filters') : getTranslation('models.table.headers.fullscreen')}
                      >
                        {isFullscreen ? (
                          <Minimize2 className="w-4 h-4" />
                        ) : (
                          <Maximize2 className="w-4 h-4" />
                        )}
                      </Button>
                    )}
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedModels.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={14} className="text-center py-8">
                    {getTranslation('models.table.empty_state')}
                  </TableCell>
                </TableRow>
              ) : (
                paginatedModels.map((model, index) => (
                  <TableRow key={model.id || `model-${index}`} className={!model.isAvailable ? "opacity-60" : ""}>
                    <TableCell>
                      <input
                        type="checkbox"
                        checked={model.id ? selectedModelIds.includes(model.id) : false}
                        onChange={() => model.id && onToggleSelection(model.id)}
                        className="h-4 w-4"
                        aria-label={getTranslation('models.table.select_model', { name: model.name || '' })}
                        title={getTranslation('models.table.select_model', { name: model.name || '' })}
                      />
                    </TableCell>
                    <TableCell>
                      {model.confidentiality === "confidential" && (
                        <span title={getTranslation('models.table.tooltips.confidential')} className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-red-100 text-red-600">
                          <Lock className="w-3 h-3" />
                        </span>
                      )}
                      {model.confidentiality === "internal" && (
                        <span title={getTranslation('models.table.tooltips.internal')} className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-yellow-100 text-yellow-600">
                          <LockKeyhole className="w-3 h-3" />
                        </span>
                      )}
                      {model.confidentiality === "public" && (
                        <span title={getTranslation('models.table.tooltips.public')} className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-600">
                          <Globe className="w-3 h-3" />
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      {model.modelCard && (
                        <span title={getTranslation('models.table.tooltips.model_card_available')} className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-600">
                          <CreditCard className="w-3 h-3" />
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      {model.status === "Deprecated" ? (
                        <Tooltip>
                          <TooltipTrigger>
                            <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 text-orange-600">
                              <AlertTriangle className="w-3 h-3" />
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{getTranslation('models.table.tooltips.deprecated')}{model.shutdownDate ? ` - ${getTranslation('models.table.tooltips.shutdown_date', { date: new Date(model.shutdownDate).toLocaleDateString() })}` : ''}</p>
                          </TooltipContent>
                        </Tooltip>
                      ) : (
                        <>
                          {model['liteLLM-provisioning'] === true && (
                            <span title={getTranslation('models.table.tooltips.available_litellm')} className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-600">
                              ✓
                            </span>
                          )}
                          {model['liteLLM-provisioning'] === false && (
                            <span title={getTranslation('models.table.tooltips.unavailable_litellm')} className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-muted text-muted-foreground">
                              ✗
                            </span>
                          )}
                        </>
                      )}
                    </TableCell>
                    <TableCell className="font-medium">{model.name}</TableCell>
                    <TableCell>{model.provider}</TableCell>
                    <TableCell>
                      {model.mode === "chat" && (
                        <span title={getTranslation('models.table.tooltips.mode_chat')} className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-600">
                          <MessageCircle className="w-3 h-3" />
                        </span>
                      )}
                      {model.mode === "embedding" && (
                        <span title={getTranslation('models.table.tooltips.mode_embedding')} className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 text-purple-600">
                          <Package className="w-3 h-3" />
                        </span>
                      )}
                      {model.mode === "image" && (
                        <span title={getTranslation('models.table.tooltips.mode_image')} className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-pink-100 text-pink-600">
                          <Image className="w-3 h-3" />
                        </span>
                      )}
                    </TableCell>
                    <TableCell>{model.contextWindow?.toLocaleString() || "N/A"}</TableCell>
                    <TableCell>{model.maxOutputTokens?.toLocaleString() || model.maxTokens?.toLocaleString() || "N/A"}</TableCell>
                    <TableCell>{formatCurrency(model.inputCostPerToken)}</TableCell>
                    <TableCell>{formatCurrency(model.outputCostPerToken)}</TableCell>
                    <TableCell>
                      {model.benchmarkData ?
                        <span className={model.benchmarkData.pass_rate_2 >= averageScore ? "text-green-600 font-bold bg-green-50 px-2 py-1 rounded" : ""}>
                          {model.benchmarkData.pass_rate_2.toFixed(1)}%
                        </span>
                        : "N/A"}
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {/* Modalities Group */}
                        <div className="flex gap-1">
                          <span title={getTranslation('models.table.tooltips.capability_vision')}
                                className={`inline-flex items-center justify-center w-5 h-5 rounded ${model.supportsVision ? "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 opacity-100" : "bg-muted text-muted-foreground opacity-50"}`}>
                            <Eye className="w-3 h-3" />
                          </span>
                          <span title={getTranslation('models.table.tooltips.capability_pdf_input')}
                                className={`inline-flex items-center justify-center w-5 h-5 rounded ${model.supportsPdfInput ? "bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400 opacity-100" : "bg-muted text-muted-foreground opacity-50"}`}>
                            <FileText className="w-3 h-3" />
                          </span>
                          <span title={getTranslation('models.table.tooltips.capability_audio_input')}
                                className={`inline-flex items-center justify-center w-5 h-5 rounded ${model.supportsAudioInput ? "bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400 opacity-100" : "bg-muted text-muted-foreground opacity-50"}`}>
                            <Headphones className="w-3 h-3" />
                          </span>
                          <span title={getTranslation('models.table.tooltips.capability_audio_output')}
                                className={`inline-flex items-center justify-center w-5 h-5 rounded ${model.supportsAudioOutput ? "bg-pink-100 text-pink-600 dark:bg-pink-900/30 dark:text-pink-400 opacity-100" : "bg-muted text-muted-foreground opacity-50"}`}>
                            <Volume2 className="w-3 h-3" />
                          </span>
                          <span title={getTranslation('models.table.tooltips.capability_embedding_image_input')}
                                className={`inline-flex items-center justify-center w-5 h-5 rounded ${model.supportsEmbeddingImageInput ? "bg-teal-100 text-teal-600 dark:bg-teal-900/30 dark:text-teal-400 opacity-100" : "bg-muted text-muted-foreground opacity-50"}`}>
                            <ImageIcon className="w-3 h-3" />
                          </span>
                        </div>
                        {/* Technical Capabilities Group */}
                        <div className="flex gap-1">
                          <span title={getTranslation('models.table.tooltips.capability_function_calling')}
                                className={`inline-flex items-center justify-center w-5 h-5 rounded ${model.supportsFunctionCalling ? "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400 opacity-100" : "bg-muted text-muted-foreground opacity-50"}`}>
                            <Settings className="w-3 h-3" />
                          </span>
                          <span title={getTranslation('models.table.tooltips.capability_prompt_caching')}
                                className={`inline-flex items-center justify-center w-5 h-5 rounded ${model.supportsPromptCaching ? "bg-orange-100 text-orange-600 dark:bg-orange-900/30 dark:text-orange-400 opacity-100" : "bg-muted text-muted-foreground opacity-50"}`}>
                            <HardDrive className="w-3 h-3" />
                          </span>
                          <span title={getTranslation('models.table.tooltips.capability_reasoning')}
                                className={`inline-flex items-center justify-center w-5 h-5 rounded ${model.supportsReasoning ? "bg-indigo-100 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400 opacity-100" : "bg-muted text-muted-foreground opacity-50"}`}>
                            <Brain className="w-3 h-3" />
                          </span>
                          <span title={getTranslation('models.table.tooltips.capability_system_messages')}
                                className={`inline-flex items-center justify-center w-5 h-5 rounded ${model.supportsSystemMessages ? "bg-cyan-100 text-cyan-600 dark:bg-cyan-900/30 dark:text-cyan-400 opacity-100" : "bg-muted text-muted-foreground opacity-50"}`}>
                            <MessageSquare className="w-3 h-3" />
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onModelSelect(model)}
                      >
                        {getTranslation('models.table.details_button')}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
    </TooltipProvider>
  );
}