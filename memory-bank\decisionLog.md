# Decision Log

This file records architectural and implementation decisions using a list format.

## Decision

**Technology Stack Selection**
- Next.js 15 with App Router for modern React development
- TypeScript for type safety and developer experience
- Tailwind CSS 4 for utility-first styling
- ShadCN UI for consistent, accessible component library

## Rationale 

- Next.js 15 provides latest React features, server components, and optimized bundling
- TypeScript ensures type safety for complex data structures (model info, benchmark data)
- Tailwind CSS 4 offers performance improvements and modern CSS features
- ShadCN UI provides production-ready components with accessibility built-in

## Implementation Details

- App Router structure in src/app/ directory
- Component organization: ui/, models/, layout/ subdirectories
- New York style variant with neutral color scheme for professional appearance
- Font configuration using <PERSON>eist family for modern typography
- Dark mode implementation via next-themes with system preference detection

---

## Decision

**Data Architecture for Model and Benchmark Information**
- Static JSON files for benchmark data (polyglot-leaderboard-*.json)
- Service layer architecture for API integration
- Context-based state management for application data

## Rationale 

- Static files ensure fast loading of benchmark data
- Service layer provides abstraction for multiple data sources (iteraGPT, LiteLLM)
- Context pattern maintains data consistency across components

## Implementation Details

- Benchmark files stored in static/ directory with date-based naming
- API services in src/services/ with dedicated modules per data source
- ModelDataContext for centralized state management
- Fallback utilities for graceful degradation when APIs unavailable

2025-06-05 11:44:52 - Initial Memory Bank creation during UMB operation with architectural decisions from project analysis.
---

## Decision

**Enhanced Benchmark Data Deduplication Strategy**
- Extended deduplication logic to compare both `modelid` AND `model` fields
- Implemented smart replacement criteria based on data quality metrics
- Added comprehensive logging for deduplication tracking

## Rationale 

- Single field comparison missed duplicates with different IDs but same model names
- Quality-based replacement ensures best available data is retained
- Enhanced logging provides transparency and debugging capability for data processing

## Implementation Details

- `deduplicateBenchmarkData()` function uses Map-based approach with custom comparison
- `shouldReplaceExistingBenchmark()` prioritizes: date → test coverage → performance → completeness
- Normalization function `normalizeModelIdentifier()` for consistent comparisons
- Console logging tracks all deduplication decisions for monitoring

[2025-06-05 11:49:01] - Enhanced deduplication logic implementation

---

## Decision

**Smart Benchmark Matching Algorithm to Avoid False Positives**
- Separated single models from combination models (containing "+", "&", "and")
- Implemented priority-based matching: exact ID → exact name → partial matches
- Added scoring system with penalties for combination model matches

## Rationale 

- Previous algorithm incorrectly matched "gpt-4.1" with "o3 (high) + gpt-4.1" (82.7%) instead of pure "gpt-4.1" (52.4%)
- Combination models represent hybrid benchmarks that shouldn't match individual models
- Priority system ensures most accurate matches while maintaining fallback capability

## Implementation Details

- `isCombinationModel()` function detects hybrid benchmarks
- Four-tier matching priority with separate processing for single vs. combination models
- Scoring system with -50 penalty for combination models in fallback matching
- Enhanced logging shows match reasoning and alternative options

[2025-06-05 11:55:46] - Smart matching algorithm implementation

---

## Decision

**Cost Visualization Enhancement with Color-Coded Transparency**
- Added dynamic color coding to benchmark cost column based on min-max scaling
- Implemented red-to-green gradient with variable transparency
- Used background color with transparency for visual cost comparison

## Rationale 

- Cost information was difficult to interpret without visual context
- Color coding provides immediate visual feedback on cost efficiency
- Transparency scaling emphasizes cost differences while maintaining readability

## Implementation Details

- `getCostColor()` function calculates normalized cost position (0-1)
- Green for low costs, red for high costs with 30%-100% transparency scaling
- Applied to TableCell in BenchmarkDetails component with rounded styling
- Graceful handling of missing cost data with muted styling

[2025-06-05 11:59:14] - Cost visualization enhancement implementation
[2025-06-05 16:18:00] - ARCHITECTURAL DECISION: Removed LiteLLM GitHub data source dependency. Rationale: User requested exclusive use of /info endpoint data for authoritative model information. Implications: Simplified data flow, reduced external dependencies, single source of truth for model capabilities and costs.
---

## Decision

**Korrektur der Feature-Mapping-Logik für /info Endpoint**
- Vollständige Transformation aller Support-Properties von snake_case zu camelCase
- Hinzugefügt fehlende `supportsReasoning` Property aus API-Response
- Erweitert UI-Anzeige um alle verfügbaren Features

## Rationale 

- API liefert Support-Properties in snake_case Format (z.B. `supports_function_calling`)
- UI verwendet camelCase Format (z.B. `supportsFunctionCalling`)  
- Ursprüngliche Transformation war unvollständig und verursachte falsche Anzeige der Features
- Beispiel azure/gpt-4.1-nano zeigte korrekte API-Daten, aber falsche UI-Darstellung

## Implementation Details

- **fetchModelInfo()**: Erweitert Transformation um alle 15 Support-Properties
- **fetchCombinedModelData()**: Ergänzt um `supportsReasoning` Property
- **ModelInfo Interface**: Hinzugefügt `supportsReasoning` in beiden Formaten
- **ModelTable UI**: Erweitert Fähigkeiten-Sektion um Reasoning-Anzeige
- Vollständige Property-Mapping: snake_case → camelCase für konsistente Datennutzung

[2025-06-05 16:26:00] - Feature-Mapping-Korrektur für korrekte UI-Anzeige aller Support-Properties

---

## Decision

**Coding-Optimized Model Recommendation Algorithm Enhancement**
- Rebalanced scoring weights to prioritize benchmark performance (45%) over cost (10%)
- Implemented 1.5x weighting for critical coding benchmarks (SWE-bench, HumanEval, LiveCodeBench, Aider-Polyglot, Terminal-bench)
- Added coding-leader bonuses based on Leanware.co 2025 analysis
- Enhanced coding use case adjustments with additional benchmark weighting and context bonuses

## Rationale

- Previous algorithm unfairly favored cheap models (DeepSeek-R1 $0.55) over superior performers (Claude Sonnet 4 $3.00)
- Cost bias was causing quality models to be underrated despite superior coding capabilities
- Leanware.co "Best LLMs for Coding 2025" analysis identified Claude Sonnet 4/Opus 4 as leaders with 72%+ SWE-bench performance
- Coding use cases require specialized benchmark evaluation beyond general performance metrics
- Enterprise users need quality-focused recommendations for production coding tasks

## Implementation Details

- **Scoring Weight Rebalancing**: Benchmark 45% (↑), Capabilities 25%, Cost 10% (↓), Latency 15% (↑), Availability 5% (↓)
- **Critical Coding Benchmarks**: SWE-bench Verified, HumanEval, MBPP+, LiveCodeBench, Aider-Polyglot, Terminal-bench receive 1.5x weighting
- **Coding-Leader Bonuses**: Claude Sonnet 4/Opus 4 (+5 points), Gemini 2.5 Pro (+4 points) for coding use cases
- **Coding Use Case Enhancements**: +10% benchmark weighting, context window bonuses (up to +3 points)
- **Enhanced Benchmark Mapping**: Extended all coding use cases with comprehensive benchmark coverage
- **Documentation Updates**: Added coding-optimizations section to explanation page with transparent algorithm details
- **Quality Preservation**: Maintained existing quality penalties for poor factuality performance

[2025-06-09 11:33:00] - Coding-optimized recommendation algorithm implementation based on industry analysis
---

## Decision

**Blog-Funktion für Hintergrundinformationen und Release Notes**
- Umfassende Blog-Architektur mit TypeScript-Typisierung und Astro-Integration
- Strukturierte Release Notes mit Changelog-System 
- Model-Background-Integration basierend auf bestehender Model Cards Infrastruktur
- React Islands Pattern für optimale Performance

## Rationale 

- Benutzeranforderung nach Blog-Funktion für Modell-Hintergrundinformationen und Release Notes
- Nutzung der bereits vorhandenen Model Cards Daten als Basis für detaillierte Analysen
- Astro-Framework ermöglicht statische Generierung bei gleichzeitiger Interaktivität durch React Islands
- Strukturierte Datenmodelle für verschiedene Content-Typen (Modell-Analysen, Release Notes, Benchmark-Analysen)
- Integration in bestehende Navigation und Layout-Struktur

## Implementation Details

- **Datenstrukturen**: [`src/types/blog.ts`](src/types/blog.ts) mit vollständiger TypeScript-Typisierung
- **Service Layer**: [`src/services/blog.ts`](src/services/blog.ts) mit Suche, Filterung und Model-Integration
- **UI-Komponenten**: BlogCard, BlogFilters, BlogOverviewIsland mit responsivem Design
- **Astro Pages**: Blog-Übersicht [`src/pages/blog/index.astro`](src/pages/blog/index.astro) und Detail-Seite [`src/pages/blog/[slug].astro`](src/pages/blog/[slug].astro)
- **Sample Data**: [`src/data/blog-posts.json`](src/data/blog-posts.json) mit 6 realistischen Blog-Posts
- **Navigation**: Blog-Link in [`src/layouts/Layout.astro`](src/layouts/Layout.astro) hinzugefügt
- **Release Notes**: Strukturierte Changelogs mit Change-Typen (added, changed, deprecated, removed, fixed, security)
- **Model-Integration**: Automatische Verknüpfung von Blog-Posts mit Model IDs und Benchmarks
- **Kategorisierung**: Model-Analyse, Release Notes, Benchmark-Analyse, Branchen-News mit visueller Unterscheidung

[2025-06-11 09:47:00] - Blog-Funktion vollständig implementiert für Modell-Hintergrundinformationen und Release Notes
---

## Decision

**GitLab Pages Asset-Loading Fix für Bilder und statische Dateien**
- Korrektur der Astro-Konfiguration mit korrekter site URL
- Fix der GitLab CI/CD Pipeline für korrekte public/ **********************
- Anpassung des Blog-Artikels an korrekte Bildpfade

## Rationale 

- GitLab Pages konnte Bilder nicht laden aufgrund fehlerhafter Pipeline-Konfiguration
- Das `public/` Verzeichnis wurde in der CI nicht korrekt erstellt vor dem Kopiervorgang
- Die Astro `site` URL war noch auf Platzhalter-Werte gesetzt
- Blog-Artikel referenzierte nicht-existierende Bilder

## Implementation Details

- **astro.config.mjs**: `site` URL auf korrekte GitLab Pages URL gesetzt (`https://iteratec-llm-browser-b6a964.pages.iteratec.de`)
- **.gitlab-ci.yml**: `mkdir -p public` vor `cp -r dist/* public/` hinzugefügt für sichere **********************
- **Blog-Artikel**: `featuredImage` auf existierendes Bild `2025-06-api.png` korrigiert
- **Kategorie-Fix**: Blog-Artikel von "release-notes" zu "branchen-news" geändert für korrekte Kategorisierung

[2025-06-11 18:00:00] - GitLab Pages Asset-Loading Fix implementiert
---

## Decision

**Korrekte GitLab Pages Asset-Loading Lösung: Astro publicDir/outDir Remapping**
- Umbenennung des `public/` Verzeichnisses zu `static/` für statische Assets
- Konfiguration von Astro `outDir: 'public'` und `publicDir: 'static'`
- Vereinfachung der GitLab CI/CD Pipeline durch direkten Build in `public/`

## Rationale 

- Astro verwendet standardmäßig das `public/` Verzeichnis für statische Assets
- GitLab Pages erwartet Build-Ausgabe im `public/` Verzeichnis
- Dies führt zu einem Namenskonflikt zwischen Astro's Asset-Verzeichnis und GitLab's Deployment-Verzeichnis
- Die offizielle Astro-Lösung ist das Remapping der Verzeichnisse

## Implementation Details

- **Verzeichnis-Umbenennung**: `mv public static` - alle statischen Assets (Bilder, SVGs, JSON) in `static/` Verzeichnis verschoben
- **astro.config.mjs**: 
  * `outDir: 'public'` - Astro baut direkt in GitLab Pages Deployment-Verzeichnis
  * `publicDir: 'static'` - Astro verwendet `static/` für statische Assets statt `public/`
- **.gitlab-ci.yml**: 
  * Build-Artifacts von `dist/` zu `public/` geändert
  * Deploy-Stage vereinfacht - kein Kopieren mehr nötig, da Astro direkt in `public/` baut
  * Entfernung der `mkdir -p public` und `cp -r dist/* public/` Befehle
- **Asset-Pfade**: Bleiben unverändert (`/images/blog/...`) da Astro automatisch aus `static/` in Website-Root kopiert

[2025-06-11 18:03:00] - Korrekte GitLab Pages Asset-Loading Lösung mit Astro publicDir/outDir Remapping implementiert
---

## Decision

**Comprehensive Optimization Strategy for Current Codebase**
- Prioritized 6-area optimization approach focusing on immediate code quality improvements
- TypeScript strict mode activation as critical first step
- Testing-first approach with 80% coverage target
- Performance optimization through bundle analysis and code splitting

## Rationale 

- Current codebase is production-ready but has significant technical debt (TypeScript strict: false, no ESLint, 0% test coverage)
- Existing optimization plans from feature-optimizations/ folder provide foundation but need current state analysis
- Service layer architecture is already well-structured (6 separate files), reducing refactoring complexity
- Bundle size optimization potential (400KB → 300KB) provides immediate user experience benefits
- Testing implementation critical for long-term maintainability and confidence in changes

## Implementation Details

- 4-phase implementation over 8 weeks (132 hours total)
- Phase 1: Code quality foundations (TypeScript strict, ESLint, pre-commit hooks)
- Phase 2: Testing infrastructure (Vitest, component tests, integration tests)
- Phase 3: Performance optimization (code splitting, image optimization, bundle reduction)
- Phase 4: Feature enhancement (advanced search, PWA, mobile optimization)
- Success metrics: Build <90s, Bundle <300KB, Lighthouse 98+, Test coverage >80%

[2025-12-06 18:02:00] - Optimization strategy decision based on current codebase analysis and existing optimization work review
[2025-06-14 08:25:00] - Navigation Architecture Enhancement with Data Transparency Implementation

## Decision

**Header Navigation Restructuring for Specialized Benchmark Access**
- Converted static "Benchmarks" navigation link to interactive dropdown menu
- Added Aider-Polyglot as specialized sub-navigation item under Benchmarks
- Enhanced data transparency with comprehensive source attribution and update frequency disclosure

## Rationale 

- User requirement for dedicated Aider-Polyglot benchmark access within existing navigation structure
- Need for better organization of benchmark-related content as project grows
- Data transparency essential for user trust and understanding of information reliability
- Specialized benchmarks deserve prominent but organized navigation placement
- Daily data updates from external source require clear communication to users

## Implementation Details

- **Navigation Structure**: Modified [`src/layouts/Layout.astro`](src/layouts/Layout.astro:38-50) with CSS-based dropdown using group hover functionality
- **Sub-Navigation Items**: "Alle Benchmarks" (main page) and "Aider-Polyglot" (specialized benchmark)
- **Visual Design**: SVG chevron icon, smooth transitions, proper z-index layering, consistent styling
- **Data Source Attribution**: Added direct link to https://aider.chat/docs/leaderboards/ in [`src/pages/benchmark/index.astro`](src/pages/benchmark/index.astro:77-82)
- **Update Frequency Disclosure**: Included "automatisch 1x täglich aktualisiert" information for user awareness
- **Accessibility Considerations**: Proper link attributes (target="_blank", rel="noopener noreferrer"), screen reader compatibility
- **Integration Approach**: Seamless integration into existing blue information box design without disrupting page layout

## Decision

**Automated Polyglot Leaderboard Data Pipeline with Dynamic File Discovery**
- Automated YAML-to-JSON conversion script for Aider polyglot leaderboard data
- Dynamic file discovery replacing static hardcoded file lists
- Monthly data filename convention with automatic current-month generation
- Data structure transformation ensuring compatibility with existing processing pipeline

## Rationale 

- Manual process for updating polyglot leaderboard data was time-consuming and error-prone
- Static file list in generate-static-data.ts required code changes for each new month's data
- Direct YAML source from Aider repository ensures data freshness and eliminates intermediate steps
- Future scalability requires zero-maintenance approach for ongoing monthly data updates
- Consistency with existing JSON structure critical for UI components and benchmark processing

## Implementation Details

- **Automation Script**: [`scripts/fetch-polyglot-leaderboard.js`](scripts/fetch-polyglot-leaderboard.js) with YAML parsing, data transformation, and file generation
- **Source Integration**: Direct fetch from https://raw.githubusercontent.com/Aider-AI/aider/refs/heads/main/aider/website/_data/polyglot_leaderboard.yml
- **Data Transformation**: YAML to JSON with modelid generation, main fields extraction, and details object organization
- **Dynamic Discovery**: [`scripts/generate-static-data.ts`](scripts/generate-static-data.ts) enhanced with `fs.readdir()` and filtering for polyglot-leaderboard-*.json files
- **Filename Convention**: `polyglot-leaderboard-YYYY-MM.json` with automatic current month detection
- **NPM Integration**: `npm run fetch:polyglot` script for easy execution
- **Future-Proof Architecture**: New monthly files automatically discovered and processed without code changes

[2025-06-14 08:30:00] - Polyglot leaderboard automation architecture implemented for zero-maintenance data pipeline

---
---