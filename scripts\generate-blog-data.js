import { readFileSync, writeFileSync, mkdirSync, readdirSync } from 'fs';
import { dirname, join, extname, basename } from 'path';
import { parse } from 'yaml';

/**
 * Converts Markdown blog posts to JSON for client-side consumption
 * This script runs during build to generate static blog data
 */
async function generateBlogData() {
  try {
    console.log('🔄 Generating blog data from Markdown files...');
    
    const contentDir = 'src/content/blog';
    const blogFiles = readdirSync(contentDir).filter(file => extname(file) === '.md');
    
    const posts = [];
    
    for (const file of blogFiles) {
      const filePath = join(contentDir, file);
      const content = readFileSync(filePath, 'utf-8');
      
      // Parse frontmatter and content
      const frontmatterMatch = content.match(/^---\r?\n([\s\S]*?)\r?\n---\r?\n?([\s\S]*)$/);
      if (!frontmatterMatch) {
        console.warn(`⚠️ Skipping ${file}: No valid frontmatter found`);
        console.warn(`   File starts with: ${content.substring(0, 50)}...`);
        continue;
      }
      
      const [, frontmatterStr, markdownContent] = frontmatterMatch;
      let frontmatter;
      try {
        frontmatter = parse(frontmatterStr);
      } catch (yamlError) {
        console.warn(`⚠️ Skipping ${file}: YAML parsing error:`, yamlError.message);
        continue;
      }
      
      // Calculate reading time if not provided
      const calculateReadingTime = (content) => {
        const wordsPerMinute = 200;
        const wordCount = content.split(/\s+/).length;
        return Math.ceil(wordCount / wordsPerMinute);
      };
      
      const slug = basename(file, '.md');
      
      const post = {
        id: slug,
        title: frontmatter.title,
        slug: slug,
        excerpt: frontmatter.excerpt,
        content: markdownContent.trim(),
        category: frontmatter.category,
        tags: frontmatter.tags || [],
        publishDate: new Date(frontmatter.publishDate).toISOString(),
        lastUpdated: frontmatter.lastUpdated ? new Date(frontmatter.lastUpdated).toISOString() : new Date(frontmatter.publishDate).toISOString(),
        author: frontmatter.author,
        readingTime: frontmatter.readingTime || calculateReadingTime(markdownContent),
        featured: frontmatter.featured || false,
        relatedModelIds: frontmatter.relatedModelIds || [],
        relatedBenchmarks: frontmatter.relatedBenchmarks || [],
        releaseVersion: frontmatter.releaseVersion,
        changelog: frontmatter.changelog || [],
        metaDescription: frontmatter.metaDescription,
        metaKeywords: frontmatter.metaKeywords || [],
        featuredImage: frontmatter.featuredImage,
        gallery: frontmatter.gallery || []
      };
      
      posts.push(post);
      console.log(`✅ Processed: ${file} (${post.category})`);
    }
    
    // Sort by publish date (newest first)
    posts.sort((a, b) => new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime());
    
    // Generate metadata
    const categories = {};
    const tags = {};
    
    posts.forEach(post => {
      categories[post.category] = (categories[post.category] || 0) + 1;
      post.tags.forEach(tag => {
        tags[tag] = (tags[tag] || 0) + 1;
      });
    });
    
    const blogData = {
      posts,
      metadata: {
        totalPosts: posts.length,
        categories,
        tags,
        latestPosts: posts.slice(0, 5),
        featuredPosts: posts.filter(post => post.featured).slice(0, 3),
        lastUpdated: new Date().toISOString()
      }
    };
    
    // Ensure directory exists
    const outputPath = 'public/data/blog-posts.json';
    mkdirSync(dirname(outputPath), { recursive: true });
    
    // Write JSON file
    writeFileSync(outputPath, JSON.stringify(blogData, null, 2));
    
    console.log(`\n📊 Blog Data Summary:`);
    console.log(`   Total Posts: ${posts.length}`);
    console.log(`   Categories: ${Object.keys(categories).length} (${Object.keys(categories).join(', ')})`);
    console.log(`   Tags: ${Object.keys(tags).length}`);
    console.log(`   Featured: ${blogData.metadata.featuredPosts.length}`);
    console.log(`   Output: ${outputPath}`);
    
  } catch (error) {
    console.error('❌ Error generating blog data:', error);
    process.exit(1);
  }
}

// Run the script
generateBlogData();