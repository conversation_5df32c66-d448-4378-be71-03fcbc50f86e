{"models": [{"id": "45d635b4878e44c81846b2c40abdf02cb5f86a58a95fe8e269c1ca8c5ffbea07", "name": "gcp/claude-3.7-sonnet", "key": "gcp/claude-3.7-sonnet", "provider": "vertex_ai-anthropic_models", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "contextWindow": 200000, "maxTokens": 8192, "maxInputTokens": 200000, "maxOutputTokens": 8192, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "Google Cloud Vertex AI Documentation, Anthropic Claude 4 Blog Post, Anthropic Models Overview, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard, MATH 500 Benchmark Update", "_modelCardVersion": "1.4", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "gcp/claude-3.7-sonnet", "modelCardFile": "claude-sonnet-3.7.json", "modelCardId": "claude-3-7-sonnet@20250219"}, "benchmarkData": {"model": "claude-3-7-sonnet-20250219 (32k thinking tokens)", "modelid": "claude-3-7-sonnet-32k-thinking", "pass_rate_2": 64.9, "percent_cases_well_formed": 97.8, "total_cost": 36.8343, "command": "aider --model anthropic/claude-3-7-sonnet-20250219 --thinking-tokens 32k", "edit_format": "diff", "details": {"dirname": "2025-02-24-21-47-23--sonnet37-diff-think-32k-64k", "test_cases": 225, "commit_hash": "60d11a6, 93edbda", "pass_rate_1": 29.3, "pass_num_1": 66, "pass_num_2": 146, "error_outputs": 66, "num_malformed_responses": 5, "num_with_malformed_responses": 5, "user_asks": 5, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 1, "total_tests": 225, "date": "2025-02-24", "versions": "0.75.1.dev", "seconds_per_case": 105.2}}}, {"id": "7e121354d7b80dd7199b648b59529ca3f1f86683a84490839da99fd223ac8cd9", "name": "anthropic/claude-3.7-sonnet", "key": "anthropic/claude-3.7-sonnet", "provider": "anthropic", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "contextWindow": 200000, "maxTokens": 128000, "maxInputTokens": 200000, "maxOutputTokens": 128000, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": true, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "Google Cloud Vertex AI Documentation, Anthropic Claude 4 Blog Post, Anthropic Models Overview, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard, MATH 500 Benchmark Update", "_modelCardVersion": "1.4", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "anthropic/claude-3.7-sonnet", "modelCardFile": "claude-sonnet-3.7.json", "modelCardId": "claude-3-7-sonnet@20250219"}, "benchmarkData": {"model": "claude-3-7-sonnet-20250219 (32k thinking tokens)", "modelid": "claude-3-7-sonnet-32k-thinking", "pass_rate_2": 64.9, "percent_cases_well_formed": 97.8, "total_cost": 36.8343, "command": "aider --model anthropic/claude-3-7-sonnet-20250219 --thinking-tokens 32k", "edit_format": "diff", "details": {"dirname": "2025-02-24-21-47-23--sonnet37-diff-think-32k-64k", "test_cases": 225, "commit_hash": "60d11a6, 93edbda", "pass_rate_1": 29.3, "pass_num_1": 66, "pass_num_2": 146, "error_outputs": 66, "num_malformed_responses": 5, "num_with_malformed_responses": 5, "user_asks": 5, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 1, "total_tests": 225, "date": "2025-02-24", "versions": "0.75.1.dev", "seconds_per_case": 105.2}}}, {"id": "116d636e3b6c757464290e4764697b56537fb6122c7d0bdd19063335264832ac", "name": "aws/claude-3.7-sonnet", "key": "aws/claude-3.7-sonnet", "provider": "bedrock_converse", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "contextWindow": 200000, "maxTokens": 8192, "maxInputTokens": 200000, "maxOutputTokens": 8192, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "Google Cloud Vertex AI Documentation, Anthropic Claude 4 Blog Post, Anthropic Models Overview, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard, MATH 500 Benchmark Update", "_modelCardVersion": "1.4", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "aws/claude-3.7-sonnet", "modelCardFile": "claude-sonnet-3.7.json", "modelCardId": "claude-3-7-sonnet@20250219"}, "benchmarkData": {"model": "claude-3-7-sonnet-20250219 (32k thinking tokens)", "modelid": "claude-3-7-sonnet-32k-thinking", "pass_rate_2": 64.9, "percent_cases_well_formed": 97.8, "total_cost": 36.8343, "command": "aider --model anthropic/claude-3-7-sonnet-20250219 --thinking-tokens 32k", "edit_format": "diff", "details": {"dirname": "2025-02-24-21-47-23--sonnet37-diff-think-32k-64k", "test_cases": 225, "commit_hash": "60d11a6, 93edbda", "pass_rate_1": 29.3, "pass_num_1": 66, "pass_num_2": 146, "error_outputs": 66, "num_malformed_responses": 5, "num_with_malformed_responses": 5, "user_asks": 5, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 1, "total_tests": 225, "date": "2025-02-24", "versions": "0.75.1.dev", "seconds_per_case": 105.2}}}, {"id": "0a77ff834ac00b7ad0fbfa5d8e7f91da255ef30e27c91dbb40c9a4014d6369c2", "name": "gcp/claude-3.5-sonnet-v2", "key": "gcp/claude-3.5-sonnet-v2", "provider": "vertex_ai-anthropic_models", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "contextWindow": 200000, "maxTokens": 8192, "maxInputTokens": 200000, "maxOutputTokens": 8192, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true, "status": "Deprecated", "deprecationDate": "2025-06-20", "shutdownDate": "2025-06-20", "_modelCardSource": "Google Cloud Vertex AI Documentation, Anthropic Documentation, SWE-bench Verified Benchmark, MATH 500 Benchmark Update", "_modelCardVersion": "1.2", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "gcp/claude-3.5-sonnet-v2", "modelCardFile": "claude-3-5-sonnet-v2.json", "modelCardId": "claude-3-5-sonnet-v2@20241022"}}, {"id": "anthropic/claude-3.5-sonnet", "name": "Claude 3.5 Sonnet v2", "provider": "Anthropic", "description": "Ein hochmodernes Modell für reale Softwareentwicklungsaufgaben und für von KI-Agenten zu übernehmende Aufgaben. Setzt neue Industriestandards für Intelligenz mit 2x der Geschwindigkeit von Claude 3 Opus.", "contextWindow": 200000, "maxOutputTokens": 8000, "maxTokens": 8000, "maxInputTokens": 200000, "supportsFunctionCalling": true, "supportsVision": true, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsSystemMessages": true, "supportsPromptCaching": true, "supportsReasoning": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsImageGeneration": false, "supportsCodeExecution": false, "supportsBatchProcessing": true, "supportsThinking": false, "supportsGrounding": false, "supportsMultilingual": true, "supportsStructuredOutputs": false, "supportsRealTimeAPI": false, "liteLLM-provisioning": true, "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "inputCostPer1kTokens": 0.003, "outputCostPer1kTokens": 0.015, "status": "Deprecated", "deprecationDate": "2025-06-20", "shutdownDate": "2025-06-20", "key": "anthropic/claude-3.5-sonnet", "isAvailable": false, "modelGroup": "<PERSON>", "mode": "chat", "confidentiality": "public", "displayName": "Claude 3.5 Sonnet v2", "mappingInfo": {"mappingKey": "anthropic/claude-3.5-sonnet", "modelCardFile": "claude-3-5-sonnet-v2.json", "modelCardId": "claude-3-5-sonnet-v2@20241022"}, "_isModelCard": true, "_modelCardSource": "Google Cloud Vertex AI Documentation, Anthropic Documentation, SWE-bench Verified Benchmark, MATH 500 Benchmark Update", "_modelCardVersion": "1.2", "_enrichedWithMapping": true}, {"id": "b78f22528436c67ef5b42f79f66aa1ea29119ff82b54f2a27befaacea8dfcd4c", "name": "gcp/claude-3.5-sonnet", "key": "gcp/claude-3.5-sonnet", "provider": "vertex_ai-anthropic_models", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "contextWindow": 200000, "maxTokens": 8192, "maxInputTokens": 200000, "maxOutputTokens": 8192, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true, "status": "Deprecated", "deprecationDate": "2025-06-20", "shutdownDate": "2025-06-20", "_modelCardSource": "Google Cloud Vertex AI Documentation, Anthropic Documentation, SWE-bench Verified Benchmark, MATH 500 Benchmark Update", "_modelCardVersion": "1.2", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "gcp/claude-3.5-sonnet", "modelCardFile": "claude-3-5-sonnet-v2.json", "modelCardId": "claude-3-5-sonnet-v2@20241022"}, "benchmarkData": {"model": "claude-3-5-sonnet-20241022", "modelid": "claude-3-5-sonnet-20241022", "pass_rate_2": 51.6, "percent_cases_well_formed": 99.6, "total_cost": 14.4063, "command": "aider --model claude-3-5-sonnet-20241022", "edit_format": "diff", "details": {"dirname": "2025-01-17-19-44-33--sonnet-baseline-jan-17", "test_cases": 225, "commit_hash": "6451d59", "pass_rate_1": 22.2, "pass_num_1": 50, "pass_num_2": 116, "error_outputs": 2, "num_malformed_responses": 1, "num_with_malformed_responses": 1, "user_asks": 11, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 8, "total_tests": 225, "date": "2025-01-17", "versions": "0.71.2.dev", "seconds_per_case": 21.4}}}, {"id": "4b5c29236cf07fcc922a7de6bc8a3bae2631253b5115c664f59dbee2574ccc12", "name": "aws/claude-3.5-sonnet", "key": "aws/claude-3.5-sonnet", "provider": "bedrock", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "contextWindow": 200000, "maxTokens": 4096, "maxInputTokens": 200000, "maxOutputTokens": 4096, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true, "status": "Deprecated", "deprecationDate": "2025-06-20", "shutdownDate": "2025-06-20", "_modelCardSource": "Google Cloud Vertex AI Documentation, Anthropic Documentation, SWE-bench Verified Benchmark, MATH 500 Benchmark Update", "_modelCardVersion": "1.2", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "aws/claude-3.5-sonnet", "modelCardFile": "claude-3-5-sonnet-v2.json", "modelCardId": "claude-3-5-sonnet-v2@20241022"}, "benchmarkData": {"model": "claude-3-5-sonnet-20241022", "modelid": "claude-3-5-sonnet-20241022", "pass_rate_2": 51.6, "percent_cases_well_formed": 99.6, "total_cost": 14.4063, "command": "aider --model claude-3-5-sonnet-20241022", "edit_format": "diff", "details": {"dirname": "2025-01-17-19-44-33--sonnet-baseline-jan-17", "test_cases": 225, "commit_hash": "6451d59", "pass_rate_1": 22.2, "pass_num_1": 50, "pass_num_2": 116, "error_outputs": 2, "num_malformed_responses": 1, "num_with_malformed_responses": 1, "user_asks": 11, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 8, "total_tests": 225, "date": "2025-01-17", "versions": "0.71.2.dev", "seconds_per_case": 21.4}}}, {"id": "b3cb6e3163d6a857deba4eb73921eade14bcd7506c5744a7cec6133b0c4eea9f", "name": "gcp/gemini-2.5-pro-preview-05-06", "key": "gcp/gemini-2.5-pro-preview-05-06", "provider": "vertex_ai-language-models", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "inputCostPerToken": 1.25e-06, "outputCostPerToken": 1e-05, "contextWindow": 1048576, "maxTokens": 65535, "maxInputTokens": 1048576, "maxOutputTokens": 65535, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": false, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": true, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "liteLLM-provisioning": true, "status": "Preview", "_modelCardSource": "Google Cloud Vertex AI Documentation, vals.ai AIME Benchmark, vals.ai MMMU Benchmark, SWE-bench Verified Benchmark, Terminal-Bench Leaderboard", "_modelCardVersion": "1.4", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "gcp/gemini-2.5-pro-preview-05-06", "modelCardFile": "gemini-2.5-pro.json", "modelCardId": "gemini-2.5-pro-preview-05-06"}, "benchmarkData": {"model": "Gemini 2.5 Pro Preview 05-06", "modelid": "gemini-2.5-pro-preview-05-06", "pass_rate_2": 76.9, "percent_cases_well_formed": 97.3, "total_cost": 37.4104, "command": "aider --model gemini/gemini-2.5-pro-preview-05-06", "edit_format": "diff-fenced", "details": {"dirname": "2025-05-07-19-32-40--gemini0506-diff-fenced-completion_cost", "test_cases": 225, "commit_hash": "3b08327-dirty", "pass_rate_1": 36.4, "pass_num_1": 82, "pass_num_2": 173, "error_outputs": 15, "num_malformed_responses": 7, "num_with_malformed_responses": 6, "user_asks": 105, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 2, "total_tests": 225, "date": "2025-05-07", "versions": "0.82.4.dev", "seconds_per_case": 165.3}}}, {"id": "891b4272b391dbb580fa154d997e7458627f93a3a609e1dbb933b1c7909ec1fb", "name": "openai/gpt-4.1", "key": "openai/gpt-4.1", "provider": "openai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "inputCostPerToken": 2e-06, "outputCostPerToken": 8e-06, "contextWindow": 1047576, "maxTokens": 32768, "maxInputTokens": 1047576, "maxOutputTokens": 32768, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": true, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "OpenAI Platform Documentation, Terminal-Bench Leaderboard", "_modelCardVersion": "1.2", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "openai/gpt-4.1", "modelCardFile": "gpt-4.1.json", "modelCardId": "gpt-4.1"}, "benchmarkData": {"model": "gpt-4.1", "modelid": "gpt-4.1", "pass_rate_2": 52.4, "percent_cases_well_formed": 98.2, "total_cost": 9.8556, "command": "aider --model gpt-4.1", "edit_format": "diff", "details": {"dirname": "2025-04-14-21-05-54--gpt41-diff-exuser", "test_cases": 225, "commit_hash": "7a87db5-dirty", "pass_rate_1": 20, "pass_num_1": 45, "pass_num_2": 118, "error_outputs": 6, "num_malformed_responses": 5, "num_with_malformed_responses": 4, "user_asks": 171, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 5, "total_tests": 225, "date": "2025-04-14", "versions": "0.81.4.dev", "seconds_per_case": 20.5}}}, {"id": "df445e425271aba43b23e6f57a74c0325f8cac1d42c7884cf97677204882d60f", "name": "azure/gpt-4.1", "key": "azure/gpt-4.1", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "inputCostPerToken": 2e-06, "outputCostPerToken": 8e-06, "contextWindow": 1047576, "maxTokens": 32768, "maxInputTokens": 1047576, "maxOutputTokens": 32768, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": true, "supportsNativeStreaming": true, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "OpenAI Platform Documentation, Terminal-Bench Leaderboard", "_modelCardVersion": "1.2", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "azure/gpt-4.1", "modelCardFile": "gpt-4.1.json", "modelCardId": "gpt-4.1"}, "benchmarkData": {"model": "gpt-4.1", "modelid": "gpt-4.1", "pass_rate_2": 52.4, "percent_cases_well_formed": 98.2, "total_cost": 9.8556, "command": "aider --model gpt-4.1", "edit_format": "diff", "details": {"dirname": "2025-04-14-21-05-54--gpt41-diff-exuser", "test_cases": 225, "commit_hash": "7a87db5-dirty", "pass_rate_1": 20, "pass_num_1": 45, "pass_num_2": 118, "error_outputs": 6, "num_malformed_responses": 5, "num_with_malformed_responses": 4, "user_asks": 171, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 5, "total_tests": 225, "date": "2025-04-14", "versions": "0.81.4.dev", "seconds_per_case": 20.5}}}, {"id": "openai/gpt-4.1-mini", "name": "GPT-4.1 mini", "provider": "OpenAI", "description": "Cost-effective version of GPT-4.1 with reduced capabilities for everyday tasks. Balances performance and affordability for high-volume usage.", "contextWindow": 1047576, "maxOutputTokens": 32768, "maxTokens": 32768, "maxInputTokens": 1047576, "supportsFunctionCalling": true, "supportsVision": true, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsSystemMessages": true, "supportsPromptCaching": true, "supportsReasoning": false, "supportsWebSearch": true, "supportsNativeStreaming": true, "supportsImageGeneration": false, "supportsCodeExecution": false, "supportsBatchProcessing": true, "supportsThinking": false, "supportsGrounding": false, "supportsMultilingual": true, "supportsStructuredOutputs": true, "supportsRealTimeAPI": false, "liteLLM-provisioning": false, "inputCostPerToken": 5e-07, "outputCostPerToken": 2e-06, "inputCostPer1kTokens": 0.0005, "outputCostPer1kTokens": 0.002, "status": "GA", "key": "openai/gpt-4.1-mini", "isAvailable": true, "modelGroup": "GPT", "mode": "chat", "confidentiality": "public", "displayName": "GPT-4.1 mini", "mappingInfo": {"mappingKey": "openai/gpt-4.1-mini", "modelCardFile": "gpt-4.1-mini.json", "modelCardId": "gpt-4.1-mini-2025-04-14"}, "_isModelCard": true, "_modelCardSource": "OpenAI Platform Documentation, Model Specifications based on GPT Family patterns", "_modelCardVersion": "1.0", "_enrichedWithMapping": true, "benchmarkData": {"model": "gpt-4.1", "modelid": "gpt-4.1", "pass_rate_2": 52.4, "percent_cases_well_formed": 98.2, "total_cost": 9.8556, "command": "aider --model gpt-4.1", "edit_format": "diff", "details": {"dirname": "2025-04-14-21-05-54--gpt41-diff-exuser", "test_cases": 225, "commit_hash": "7a87db5-dirty", "pass_rate_1": 20, "pass_num_1": 45, "pass_num_2": 118, "error_outputs": 6, "num_malformed_responses": 5, "num_with_malformed_responses": 4, "user_asks": 171, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 5, "total_tests": 225, "date": "2025-04-14", "versions": "0.81.4.dev", "seconds_per_case": 20.5}}}, {"id": "openai/gpt-4.1-nano", "name": "GPT-4.1 nano", "provider": "OpenAI", "description": "Fastest, most cost-effective GPT-4.1 model", "contextWindow": 1047576, "maxOutputTokens": 32768, "maxTokens": 32768, "maxInputTokens": 1047576, "supportsFunctionCalling": true, "supportsVision": true, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsSystemMessages": true, "supportsPromptCaching": true, "supportsReasoning": false, "supportsWebSearch": false, "supportsNativeStreaming": true, "supportsImageGeneration": true, "supportsCodeExecution": false, "supportsBatchProcessing": true, "supportsThinking": false, "supportsGrounding": false, "supportsMultilingual": true, "supportsStructuredOutputs": true, "supportsRealTimeAPI": false, "liteLLM-provisioning": true, "inputCostPerToken": 1.0000000000000001e-07, "outputCostPerToken": 4.0000000000000003e-07, "inputCostPer1kTokens": 0.0001, "outputCostPer1kTokens": 0.0004, "status": "GA", "key": "openai/gpt-4.1-nano", "isAvailable": true, "modelGroup": "GPT", "mode": "chat", "confidentiality": "public", "displayName": "GPT-4.1 nano", "mappingInfo": {"mappingKey": "openai/gpt-4.1-nano", "modelCardFile": "gpt-4.1-nano.json", "modelCardId": "gpt-4.1-nano-2025-04-14"}, "_isModelCard": true, "_modelCardSource": "OpenAI Platform Documentation", "_modelCardVersion": "1.1", "_enrichedWithMapping": true, "benchmarkData": {"model": "gpt-4.1", "modelid": "gpt-4.1", "pass_rate_2": 52.4, "percent_cases_well_formed": 98.2, "total_cost": 9.8556, "command": "aider --model gpt-4.1", "edit_format": "diff", "details": {"dirname": "2025-04-14-21-05-54--gpt41-diff-exuser", "test_cases": 225, "commit_hash": "7a87db5-dirty", "pass_rate_1": 20, "pass_num_1": 45, "pass_num_2": 118, "error_outputs": 6, "num_malformed_responses": 5, "num_with_malformed_responses": 4, "user_asks": 171, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 5, "total_tests": 225, "date": "2025-04-14", "versions": "0.81.4.dev", "seconds_per_case": 20.5}}}, {"id": "e083bf210e7b7927ecb9777966c7f4353b3c75f00e6100e959be9e9d2b24a87b", "name": "azure/gpt-4.1-nano", "key": "azure/gpt-4.1-nano", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "inputCostPerToken": 1e-07, "outputCostPerToken": 4e-07, "contextWindow": 1047576, "maxTokens": 32768, "maxInputTokens": 1047576, "maxOutputTokens": 32768, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": true, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "OpenAI Platform Documentation", "_modelCardVersion": "1.1", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "azure/gpt-4.1-nano", "modelCardFile": "gpt-4.1-nano.json", "modelCardId": "gpt-4.1-nano-2025-04-14"}, "benchmarkData": {"model": "gpt-4.1-nano", "modelid": "gpt-4.1-nano", "pass_rate_2": 8.9, "percent_cases_well_formed": 94.2, "total_cost": 0.4281, "command": "aider --model gpt-4.1-nano", "edit_format": "whole", "details": {"dirname": "2025-04-14-22-46-01--gpt41nano-diff", "test_cases": 225, "commit_hash": "71d1591-dirty", "pass_rate_1": 3.1, "pass_num_1": 7, "pass_num_2": 20, "error_outputs": 20, "num_malformed_responses": 20, "num_with_malformed_responses": 13, "user_asks": 316, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 8, "total_tests": 225, "date": "2025-04-14", "versions": "0.81.4.dev", "seconds_per_case": 12}}}, {"id": "3572924e258b8d6a90631d41b3db2d1b9d3a2be520e09a50e99243c9e0f3b5d0", "name": "gcp/gemini-2.0-flash", "key": "gcp/gemini-2.0-flash", "provider": "vertex_ai-language-models", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "inputCostPerToken": 1.5e-07, "outputCostPerToken": 6e-07, "contextWindow": 1048576, "maxTokens": 8192, "maxInputTokens": 1048576, "maxOutputTokens": 8192, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": false, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": true, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": true, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true, "status": "Deprecated", "deprecationDate": "2025-06-20", "shutdownDate": "2025-06-20", "_modelCardSource": "Google Cloud Vertex AI Documentation, VALS.ai, Vellum.ai, WebDev Arena, MATH 500 Benchmark Update", "_modelCardVersion": "1.1", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "gcp/gemini-2.0-flash", "modelCardFile": "gemini-2.0-flash.json", "modelCardId": "gemini-2.0-flash-001"}, "benchmarkData": {"model": "gemini-2.0-flash-exp", "modelid": "gemini-2.0-flash-exp", "pass_rate_2": 22.2, "percent_cases_well_formed": 100, "total_cost": 0, "command": "aider --model gemini/gemini-2.0-flash-exp", "edit_format": "whole", "details": {"dirname": "2024-12-22-20-08-13--gemini-2.0-flash-exp-polyglot-whole", "test_cases": 225, "commit_hash": "b1bc2f8", "pass_rate_1": 11.6, "pass_num_1": 26, "pass_num_2": 50, "error_outputs": 1, "num_malformed_responses": 0, "num_with_malformed_responses": 0, "user_asks": 9, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 8, "total_tests": 225, "date": "2024-12-22", "versions": "0.69.2.dev", "seconds_per_case": 12.2}}}, {"id": "c613d56ff09282d3bf42d8db5e096aabb2cb1af195c252318c9900b5c15f3881", "name": "anthropic/claude-opus-4", "key": "anthropic/claude-opus-4", "provider": "anthropic", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "inputCostPerToken": 1.5e-05, "outputCostPerToken": 7.5e-05, "contextWindow": 200000, "maxTokens": 32000, "maxInputTokens": 200000, "maxOutputTokens": 32000, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "Google Cloud Vertex AI Documentation, Anthropic Blog Post, Anthropic Documentation, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard", "_modelCardVersion": "1.2", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "anthropic/claude-opus-4", "modelCardFile": "claude-opus-4.json", "modelCardId": "claude-opus-4@20250514"}, "benchmarkData": {"model": "claude-opus-4-20250514 (32k thinking)", "modelid": "claude-opus-4-20250514-32k-thinking", "pass_rate_2": 72, "percent_cases_well_formed": 97.3, "total_cost": 65.7484, "command": "aider --model claude-opus-4-20250514", "edit_format": "diff", "details": {"dirname": "2025-05-25-20-40-51--opus4-diff-exuser", "test_cases": 225, "commit_hash": "9ef3211", "pass_rate_1": 37.3, "pass_num_1": 84, "pass_num_2": 162, "error_outputs": 10, "num_malformed_responses": 6, "num_with_malformed_responses": 6, "user_asks": 97, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 4, "total_tests": 225, "date": "2025-05-25", "versions": "0.83.3.dev", "seconds_per_case": 44.1, "thinking_tokens": 32000, "prompt_tokens": 2567514, "completion_tokens": 363142}}}, {"id": "gcp/claude-opus-4", "name": "<PERSON> 4", "provider": "Anthropic", "description": "Das bisher leistungsstärkste Modell von Anthropic und das modernste Codierungsmodell. Claude Opus 4 bietet eine konstante Leistung bei langwierigen Aufgaben, die konzentrierte Anstrengungen und Tausende von Schritten erfordern.", "contextWindow": 200000, "maxOutputTokens": 32000, "maxTokens": 32000, "maxInputTokens": 200000, "supportsFunctionCalling": true, "supportsVision": true, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsSystemMessages": true, "supportsPromptCaching": true, "supportsReasoning": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsImageGeneration": false, "supportsCodeExecution": false, "supportsBatchProcessing": true, "supportsThinking": true, "supportsGrounding": false, "supportsMultilingual": true, "supportsStructuredOutputs": false, "supportsRealTimeAPI": false, "liteLLM-provisioning": true, "inputCostPerToken": 1.5e-05, "outputCostPerToken": 7.5e-05, "inputCostPer1kTokens": 0.015, "outputCostPer1kTokens": 0.075, "status": "GA", "key": "gcp/claude-opus-4", "isAvailable": true, "modelGroup": "<PERSON>", "mode": "chat", "confidentiality": "public", "displayName": "<PERSON> 4", "mappingInfo": {"mappingKey": "gcp/claude-opus-4", "modelCardFile": "claude-opus-4.json", "modelCardId": "claude-opus-4@20250514"}, "_isModelCard": true, "_modelCardSource": "Google Cloud Vertex AI Documentation, Anthropic Blog Post, Anthropic Documentation, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard", "_modelCardVersion": "1.2", "_enrichedWithMapping": true, "benchmarkData": {"model": "claude-opus-4-20250514 (32k thinking)", "modelid": "claude-opus-4-20250514-32k-thinking", "pass_rate_2": 72, "percent_cases_well_formed": 97.3, "total_cost": 65.7484, "command": "aider --model claude-opus-4-20250514", "edit_format": "diff", "details": {"dirname": "2025-05-25-20-40-51--opus4-diff-exuser", "test_cases": 225, "commit_hash": "9ef3211", "pass_rate_1": 37.3, "pass_num_1": 84, "pass_num_2": 162, "error_outputs": 10, "num_malformed_responses": 6, "num_with_malformed_responses": 6, "user_asks": 97, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 4, "total_tests": 225, "date": "2025-05-25", "versions": "0.83.3.dev", "seconds_per_case": 44.1, "thinking_tokens": 32000, "prompt_tokens": 2567514, "completion_tokens": 363142}}}, {"id": "0822ca65041b4874826203897edfe68f15cb7fb668f8a502ce04068f4d67ec21", "name": "anthropic/claude-sonnet-4", "key": "anthropic/claude-sonnet-4", "provider": "anthropic", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "contextWindow": 200000, "maxTokens": 64000, "maxInputTokens": 200000, "maxOutputTokens": 64000, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "Google Cloud Vertex AI Documentation, Anthropic Blog Post, Anthropic Documentation, vals.ai AIME Benchmark, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard", "_modelCardVersion": "1.3", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "anthropic/claude-sonnet-4", "modelCardFile": "claude-sonnet-4.json", "modelCardId": "claude-sonnet-4@20250514"}, "benchmarkData": {"model": "claude-sonnet-4-20250514 (32k thinking)", "modelid": "claude-sonnet-4-20250514-32k-thinking", "pass_rate_2": 61.3, "percent_cases_well_formed": 97.3, "total_cost": 26.5755, "command": "aider --model claude-sonnet-4-20250514", "edit_format": "diff", "details": {"dirname": "2025-05-24-22-10-36--sonnet4-diff-exuser-think32k", "test_cases": 225, "commit_hash": "e3cb907", "pass_rate_1": 25.8, "pass_num_1": 58, "pass_num_2": 138, "error_outputs": 10, "num_malformed_responses": 10, "num_with_malformed_responses": 6, "user_asks": 111, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 6, "total_tests": 225, "date": "2025-05-24", "versions": "0.83.3.dev", "seconds_per_case": 79.9, "thinking_tokens": 32000, "prompt_tokens": 2863068, "completion_tokens": 1271074}}}, {"id": "97fc12f52e6d7c6ee714a3d7233f8d31ae779d6c9f750754d0853789e2b21867", "name": "gcp/claude-sonnet-4", "key": "gcp/claude-sonnet-4", "provider": "vertex_ai-anthropic_models", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "contextWindow": 200000, "maxTokens": 64000, "maxInputTokens": 200000, "maxOutputTokens": 64000, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": true, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "Google Cloud Vertex AI Documentation, Anthropic Blog Post, Anthropic Documentation, vals.ai AIME Benchmark, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard", "_modelCardVersion": "1.3", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "gcp/claude-sonnet-4", "modelCardFile": "claude-sonnet-4.json", "modelCardId": "claude-sonnet-4@20250514"}, "benchmarkData": {"model": "claude-sonnet-4-20250514 (32k thinking)", "modelid": "claude-sonnet-4-20250514-32k-thinking", "pass_rate_2": 61.3, "percent_cases_well_formed": 97.3, "total_cost": 26.5755, "command": "aider --model claude-sonnet-4-20250514", "edit_format": "diff", "details": {"dirname": "2025-05-24-22-10-36--sonnet4-diff-exuser-think32k", "test_cases": 225, "commit_hash": "e3cb907", "pass_rate_1": 25.8, "pass_num_1": 58, "pass_num_2": 138, "error_outputs": 10, "num_malformed_responses": 10, "num_with_malformed_responses": 6, "user_asks": 111, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 6, "total_tests": 225, "date": "2025-05-24", "versions": "0.83.3.dev", "seconds_per_case": 79.9, "thinking_tokens": 32000, "prompt_tokens": 2863068, "completion_tokens": 1271074}}}, {"id": "a8b22052005b11412b35b2b08bf7d384fe1c234745c92b29eefbd8aab7227b8a", "name": "gcp/gemini-2.5-flash-preview-05-20", "key": "gcp/gemini-2.5-flash-preview-05-20", "provider": "vertex_ai-language-models", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "inputCostPerToken": 1.5e-07, "outputCostPerToken": 6e-07, "contextWindow": 1048576, "maxTokens": 65535, "maxInputTokens": 1048576, "maxOutputTokens": 65535, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": false, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": true, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "liteLLM-provisioning": true, "status": "Preview", "_modelCardSource": "Google Cloud Vertex AI Documentation, SWE-bench Verified Benchmark, Terminal-Bench Leaderboard", "_modelCardVersion": "1.2", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "gcp/gemini-2.5-flash-preview-05-20", "modelCardFile": "gemini-2.5-flash.json", "modelCardId": "gemini-2.5-flash-preview-05-20"}, "benchmarkData": {"model": "gemini-2.5-flash-preview-05-20 (24k think)", "modelid": "gemini-2.5-flash-preview-05-20-24k-think", "pass_rate_2": 55.1, "percent_cases_well_formed": 95.6, "total_cost": 8.5625, "command": "aider --model gemini/gemini-2.5-flash-preview-05-20", "edit_format": "diff", "details": {"dirname": "2025-05-25-22-58-44--flash25-05-20-24k-think", "test_cases": 225, "commit_hash": "a8568c3-dirty", "pass_rate_1": 26.2, "pass_num_1": 59, "pass_num_2": 124, "error_outputs": 15, "num_malformed_responses": 15, "num_with_malformed_responses": 10, "user_asks": 101, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 4, "total_tests": 225, "date": "2025-05-25", "versions": "0.83.3.dev", "seconds_per_case": 53.9, "thinking_tokens": 24576, "prompt_tokens": 3666792, "completion_tokens": 2703162}}}, {"id": "23f2c965faa5bde3700b55bad15992c65e89e8c203de01ada55ad7265d8012c8", "name": "openai/o3", "key": "openai/o3", "provider": "openai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "inputCostPerToken": 1.5e-05, "outputCostPerToken": 6e-05, "contextWindow": 200000, "maxTokens": 100000, "maxInputTokens": 200000, "maxOutputTokens": 100000, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "DataCamp o4-mini Artikel, OpenAI Documentation, Benchmark-Vergleiche, vals.ai AIME Benchmark, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard, OpenAI Pricing Update June 2025", "_modelCardVersion": "1.5", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "openai/o3", "modelCardFile": "o3.json", "modelCardId": "o3-2025-04-16"}, "benchmarkData": {"model": "o3", "modelid": "o3", "pass_rate_2": 79.6, "percent_cases_well_formed": 95.1, "total_cost": 111.0325, "command": "aider --model o3", "edit_format": "diff", "details": {"dirname": "2025-04-16-21-20-55--o3-high-diff-temp0-exsys", "test_cases": 225, "commit_hash": "24805ff-dirty", "pass_rate_1": 36.9, "pass_num_1": 83, "pass_num_2": 179, "error_outputs": 11, "num_malformed_responses": 11, "num_with_malformed_responses": 11, "user_asks": 110, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 2, "total_tests": 225, "date": "2025-04-16", "versions": "0.82.1.dev", "seconds_per_case": 113.8}}}, {"id": "4067cbebbc6310ab092389d3290528466b55072a473f5cb8a70ef8ee3b0e1dd6", "name": "azure/o3", "key": "azure/o3", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "inputCostPerToken": 1e-05, "outputCostPerToken": 4e-05, "contextWindow": 200000, "maxTokens": 100000, "maxInputTokens": 200000, "maxOutputTokens": 100000, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "DataCamp o4-mini Artikel, OpenAI Documentation, Benchmark-Vergleiche, vals.ai AIME Benchmark, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard, OpenAI Pricing Update June 2025", "_modelCardVersion": "1.5", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "azure/o3", "modelCardFile": "o3.json", "modelCardId": "o3-2025-04-16"}, "benchmarkData": {"model": "o3", "modelid": "o3", "pass_rate_2": 79.6, "percent_cases_well_formed": 95.1, "total_cost": 111.0325, "command": "aider --model o3", "edit_format": "diff", "details": {"dirname": "2025-04-16-21-20-55--o3-high-diff-temp0-exsys", "test_cases": 225, "commit_hash": "24805ff-dirty", "pass_rate_1": 36.9, "pass_num_1": 83, "pass_num_2": 179, "error_outputs": 11, "num_malformed_responses": 11, "num_with_malformed_responses": 11, "user_asks": 110, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 2, "total_tests": 225, "date": "2025-04-16", "versions": "0.82.1.dev", "seconds_per_case": 113.8}}}, {"id": "openai/o3-mini", "name": "o3-mini", "provider": "OpenAI", "description": "Das neueste kleine Reasoning-<PERSON><PERSON> von OpenAI, das hohe Intelligenz bei gleichen Kosten- und Latenzzielen wie o1-mini bietet. WIRD AB 12.06.2025 DURCH O4-MINI ERSETZT.", "contextWindow": 200000, "maxOutputTokens": 100000, "maxTokens": 100000, "maxInputTokens": 200000, "maxReasoningTokens": 100000, "maxCompletionTokens": 100000, "supportsFunctionCalling": true, "supportsVision": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsSystemMessages": false, "supportsPromptCaching": true, "supportsReasoning": true, "supportsWebSearch": false, "supportsNativeStreaming": true, "supportsImageGeneration": true, "supportsCodeExecution": true, "supportsBatchProcessing": true, "supportsThinking": true, "supportsGrounding": false, "supportsMultilingual": true, "supportsStructuredOutputs": true, "supportsRealTimeAPI": false, "liteLLM-provisioning": true, "inputCostPerToken": 1.1e-06, "outputCostPerToken": 4.4e-06, "inputCostPer1kTokens": 0.0011, "outputCostPer1kTokens": 0.0044, "status": "DEPRECATED", "key": "openai/o3-mini", "isAvailable": false, "modelGroup": "o3", "mode": "chat", "confidentiality": "public", "displayName": "o3-mini", "mappingInfo": {"mappingKey": "openai/o3-mini", "modelCardFile": "o3-mini.json", "modelCardId": "o3-mini-2025-01-31"}, "_isModelCard": true, "_modelCardSource": "OpenAI Platform Documentation, vals.ai AIME Benchmark, MATH 500 Benchmark Update", "_modelCardVersion": "1.2", "_enrichedWithMapping": true, "benchmarkData": {"model": "o3-mini (medium)", "modelid": "o3-mini-medium", "pass_rate_2": 53.8, "percent_cases_well_formed": 95.1, "total_cost": 8.8599, "command": "aider --model o3-mini", "edit_format": "diff", "details": {"dirname": "2025-01-31-20-27-46--o3-mini-diff2", "test_cases": 225, "commit_hash": "2fb517b-dirty", "pass_rate_1": 19.1, "pass_num_1": 43, "pass_num_2": 121, "error_outputs": 28, "num_malformed_responses": 28, "num_with_malformed_responses": 11, "user_asks": 17, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 2, "total_tests": 225, "date": "2025-01-31", "versions": "0.72.4.dev", "seconds_per_case": 47.2}}}, {"id": "openai/o3-pro", "name": "o3-pro", "provider": "OpenAI", "description": "Version of o3 with more compute for better responses. Designed to tackle tough problems with advanced reasoning capabilities.", "contextWindow": 200000, "maxOutputTokens": 100000, "maxTokens": 100000, "maxInputTokens": 200000, "maxReasoningTokens": 100000, "maxCompletionTokens": 100000, "supportsFunctionCalling": true, "supportsVision": true, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsSystemMessages": true, "supportsPromptCaching": false, "supportsReasoning": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsImageGeneration": true, "supportsCodeExecution": false, "supportsBatchProcessing": true, "supportsThinking": true, "supportsGrounding": false, "supportsMultilingual": true, "supportsStructuredOutputs": true, "supportsRealTimeAPI": false, "liteLLM-provisioning": false, "inputCostPerToken": 2e-05, "outputCostPerToken": 8e-05, "inputCostPer1kTokens": 0.02, "outputCostPer1kTokens": 0.08, "status": "GA", "key": "openai/o3-pro", "isAvailable": true, "modelGroup": "o3", "mode": "chat", "confidentiality": "public", "displayName": "o3-pro", "mappingInfo": {"mappingKey": "openai/o3-pro", "modelCardFile": "o3-pro.json", "modelCardId": "o3-pro-2025-06-10"}, "_isModelCard": true, "_modelCardSource": "OpenAI Documentation", "_modelCardVersion": "1.1", "_enrichedWithMapping": true}, {"id": "ac724506793c23497031cb162cb250246e935e2714ac61dab66d44674f967e8a", "name": "openai/gpt-4o", "key": "openai/gpt-4o", "provider": "openai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "inputCostPerToken": 2.5e-06, "outputCostPerToken": 1e-05, "contextWindow": 128000, "maxTokens": 16384, "maxInputTokens": 128000, "maxOutputTokens": 16384, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "OpenAI Platform Documentation, SWE-bench Verified Benchmark, MATH 500 Benchmark Update", "_modelCardVersion": "1.2", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "openai/gpt-4o", "modelCardFile": "gpt-4o.json", "modelCardId": "gpt-4o-2024-08-06"}, "benchmarkData": {"model": "gpt-4o-2024-08-06", "modelid": "gpt-4o-2024-08-06", "pass_rate_2": 23.1, "percent_cases_well_formed": 94.2, "total_cost": 7.0286, "command": "aider --model gpt-4o-2024-08-06", "edit_format": "diff", "details": {"dirname": "2024-12-30-20-44-54--gpt4o-ex-as-sys-clean-prompt", "test_cases": 225, "commit_hash": "09ee197-dirty", "pass_rate_1": 4.9, "pass_num_1": 11, "pass_num_2": 52, "error_outputs": 21, "num_malformed_responses": 21, "num_with_malformed_responses": 13, "user_asks": 65, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 3, "total_tests": 225, "date": "2024-12-30", "versions": "0.70.1.dev", "seconds_per_case": 16}}}, {"id": "a71d34a1a02f35df7fe78cc81817d993bb41333cc3429ebed5ab40fda829baca", "name": "azure/gpt-4o", "key": "azure/gpt-4o", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "inputCostPerToken": 2.5e-06, "outputCostPerToken": 1e-05, "contextWindow": 128000, "maxTokens": 16384, "maxInputTokens": 128000, "maxOutputTokens": 16384, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "OpenAI Platform Documentation, SWE-bench Verified Benchmark, MATH 500 Benchmark Update", "_modelCardVersion": "1.2", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "azure/gpt-4o", "modelCardFile": "gpt-4o.json", "modelCardId": "gpt-4o-2024-08-06"}, "benchmarkData": {"model": "gpt-4o-2024-08-06", "modelid": "gpt-4o-2024-08-06", "pass_rate_2": 23.1, "percent_cases_well_formed": 94.2, "total_cost": 7.0286, "command": "aider --model gpt-4o-2024-08-06", "edit_format": "diff", "details": {"dirname": "2024-12-30-20-44-54--gpt4o-ex-as-sys-clean-prompt", "test_cases": 225, "commit_hash": "09ee197-dirty", "pass_rate_1": 4.9, "pass_num_1": 11, "pass_num_2": 52, "error_outputs": 21, "num_malformed_responses": 21, "num_with_malformed_responses": 13, "user_asks": 65, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 3, "total_tests": 225, "date": "2024-12-30", "versions": "0.70.1.dev", "seconds_per_case": 16}}}, {"id": "52e4bcbd1fa5f2127c714f1d60cd7c5c0aa962254312ccc51aad79049a2780ce", "name": "gcp/mistral-large-2411", "key": "gcp/mistral-large-2411", "provider": "vertex_ai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "inputCostPerToken": 2e-06, "outputCostPerToken": 6e-06, "contextWindow": null, "maxTokens": null, "maxInputTokens": null, "maxOutputTokens": null, "supportsFunctionCalling": false, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": false, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": false, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "Google Cloud Vertex AI Documentation", "_modelCardVersion": "1.0", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "gcp/mistral-large-2411", "modelCardFile": "mistral-large-2411.json", "modelCardId": "mistral-large-2411"}}, {"id": "9b16155c998c0b236dfe1e26efb2a9e31fc333c97abfb7364db3a4c1255eb03b", "name": "openai/o4-mini", "key": "openai/o4-mini", "provider": "openai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "inputCostPerToken": 1.1e-06, "outputCostPerToken": 4.4e-06, "contextWindow": 200000, "maxTokens": 100000, "maxInputTokens": 200000, "maxOutputTokens": 100000, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": false, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "DataCamp o4-mini Artikel, OpenAI Platform Documentation, vals.ai AIME Benchmark, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard", "_modelCardVersion": "1.4", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "openai/o4-mini", "modelCardFile": "o4-mini.json", "modelCardId": "o4-mini-2025-04-16"}, "benchmarkData": {"model": "o4-mini (high)", "modelid": "o4-mini-high", "pass_rate_2": 72, "percent_cases_well_formed": 90.7, "total_cost": 19.6399, "command": "aider --model o4-mini", "edit_format": "diff", "details": {"dirname": "2025-04-16-22-01-58--o4-mini-high-diff-exsys", "test_cases": 225, "commit_hash": "b66901f-dirty", "pass_rate_1": 19.6, "pass_num_1": 44, "pass_num_2": 162, "error_outputs": 26, "num_malformed_responses": 24, "num_with_malformed_responses": 21, "user_asks": 66, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 2, "total_tests": 225, "date": "2025-04-16", "versions": "0.82.1.dev", "seconds_per_case": 176.5}}}, {"id": "73ec4cb691af7b141b3d42de3a3a94cf85ad95ebf15900bd395ac4a31cf8e9f5", "name": "openai/gpt-4o-mini", "key": "openai/gpt-4o-mini", "provider": "openai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "inputCostPerToken": 1.5e-07, "outputCostPerToken": 6e-07, "contextWindow": 128000, "maxTokens": 16384, "maxInputTokens": 128000, "maxOutputTokens": 16384, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": true, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": true, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": false, "status": "GA", "_modelCardSource": "OpenAI Platform Documentation", "_modelCardVersion": "1.0", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "openai/gpt-4o-mini", "modelCardFile": "gpt-4o-mini.json", "modelCardId": "gpt-4o-mini-2024-07-18"}, "benchmarkData": {"model": "gpt-4o-mini-2024-07-18", "modelid": "gpt-4o-mini-2024-07-18", "pass_rate_2": 3.6, "percent_cases_well_formed": 100, "total_cost": 0.3236, "command": "aider --model gpt-4o-mini-2024-07-18", "edit_format": "whole", "details": {"dirname": "2024-12-21-18-41-18--polyglot-gpt-4o-mini", "test_cases": 225, "commit_hash": "a755079-dirty", "pass_rate_1": 0.9, "pass_num_1": 2, "pass_num_2": 8, "error_outputs": 0, "num_malformed_responses": 0, "num_with_malformed_responses": 0, "user_asks": 36, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 3, "total_tests": 225, "date": "2024-12-21", "versions": "0.69.2.dev", "seconds_per_case": 17.3}}}, {"id": "1384597a6446d53ec9618bb6b4cb66e6dce29601166c128e63f4f4001de0a7d4", "name": "azure/gpt-4o-mini", "key": "azure/gpt-4o-mini", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "inputCostPerToken": 1.65e-07, "outputCostPerToken": 6.6e-07, "contextWindow": 128000, "maxTokens": 16384, "maxInputTokens": 128000, "maxOutputTokens": 16384, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": false, "status": "GA", "_modelCardSource": "OpenAI Platform Documentation", "_modelCardVersion": "1.0", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "azure/gpt-4o-mini", "modelCardFile": "gpt-4o-mini.json", "modelCardId": "gpt-4o-mini-2024-07-18"}, "benchmarkData": {"model": "gpt-4o-mini-2024-07-18", "modelid": "gpt-4o-mini-2024-07-18", "pass_rate_2": 3.6, "percent_cases_well_formed": 100, "total_cost": 0.3236, "command": "aider --model gpt-4o-mini-2024-07-18", "edit_format": "whole", "details": {"dirname": "2024-12-21-18-41-18--polyglot-gpt-4o-mini", "test_cases": 225, "commit_hash": "a755079-dirty", "pass_rate_1": 0.9, "pass_num_1": 2, "pass_num_2": 8, "error_outputs": 0, "num_malformed_responses": 0, "num_with_malformed_responses": 0, "user_asks": 36, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 3, "total_tests": 225, "date": "2024-12-21", "versions": "0.69.2.dev", "seconds_per_case": 17.3}}}, {"id": "cca88e0711911265f786e93308c05b9bd5b0f2a1dd07498e08decdc396a4bb54", "name": "iteratec/Qwen2.5-Coder-32B-Instruct", "key": "iteratec/Qwen2.5-Coder-32B-Instruct", "provider": "hosted_vllm", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "confidential", "mode": "chat", "inputCostPerToken": 0, "outputCostPerToken": 0, "contextWindow": 32768, "maxTokens": 32768, "maxInputTokens": 32768, "maxOutputTokens": 8192, "supportsFunctionCalling": false, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": false, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": false, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "<PERSON><PERSON>, Benchmark Results, Lambda AI Documentation", "_modelCardVersion": "1.0", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "iteratec/Qwen2.5-Coder-32B-Instruct", "modelCardFile": "qwen2.5-coder-32b-instruct.json", "modelCardId": "qwen2.5-coder-32b-instruct"}, "benchmarkData": {"model": "Qwen2.5-Coder-32B-Instruct", "modelid": "qwen2.5-coder-32b-instruct-whole", "pass_rate_2": 16.4, "percent_cases_well_formed": 99.6, "total_cost": 0, "command": "aider --model openai/Qwen2.5-Coder-32B-Instruct", "edit_format": "whole", "details": {"dirname": "2024-12-26-00-55-20--Qwen2.5-Coder-32B-Instruct", "test_cases": 225, "commit_hash": "b51768b0", "pass_rate_1": 4.9, "pass_num_1": 11, "pass_num_2": 37, "error_outputs": 1, "num_malformed_responses": 1, "num_with_malformed_responses": 1, "user_asks": 33, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 6, "total_tests": 225, "date": "2024-12-26", "versions": "0.69.2.dev", "seconds_per_case": 42}}}, {"id": "f405328a228f46906a4e02d35a4b21a60752bb5b6e841617c952f035515efc46", "name": "azure/DeepSeek-R1", "key": "azure/DeepSeek-R1", "provider": "azure_ai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "public", "mode": "chat", "inputCostPerToken": 1.35e-06, "outputCostPerToken": 5.4e-06, "contextWindow": 128000, "maxTokens": 8192, "maxInputTokens": 128000, "maxOutputTokens": 8192, "supportsFunctionCalling": false, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": false, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "liteLLM-provisioning": true, "status": "GA", "_modelCardSource": "DeepSeek API Documentation, vals.ai AIME Benchmark, Terminal-Bench Leaderboard, MATH 500 Benchmark Update", "_modelCardVersion": "1.3", "_enrichedWithMapping": true, "mappingInfo": {"mappingKey": "azure/DeepSeek-R1", "modelCardFile": "deepseek-r1.json", "modelCardId": "deepseek-r1-0528"}, "benchmarkData": {"model": "DeepSeek R1 (0528)", "modelid": "deepseek-r1-0528", "pass_rate_2": 71.4, "percent_cases_well_formed": 94.6, "total_cost": 4.8016, "command": "aider --model deepseek/deepseek-reasoner", "edit_format": "diff", "details": {"dirname": "2025-06-06-16-47-07--r1-diff", "test_cases": 224, "commit_hash": "4c161f9-dirty", "pass_rate_1": 34.4, "pass_num_1": 77, "pass_num_2": 160, "error_outputs": 28, "num_malformed_responses": 15, "num_with_malformed_responses": 12, "user_asks": 105, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 2, "total_tests": 225, "date": "2025-06-06", "versions": "0.84.1.dev", "seconds_per_case": 716.6, "prompt_tokens": 2644169, "completion_tokens": 1842168}}}, {"id": "deepseek/deepseek-reasoner", "name": "DeepSeek-R1", "provider": "DeepSeek", "description": "Ein fortschrittliches Reasoning-Modell mit verbesserter Denktiefe und Inferenz-Fähigkeiten, das mit führenden Modellen wie O3 und Gemini 2.5 Pro konkurriert", "contextWindow": 65536, "maxOutputTokens": 65536, "maxTokens": 65536, "maxInputTokens": 65536, "supportsFunctionCalling": true, "supportsVision": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsSystemMessages": false, "supportsPromptCaching": true, "supportsReasoning": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsImageGeneration": false, "supportsCodeExecution": false, "supportsBatchProcessing": false, "supportsThinking": true, "supportsGrounding": false, "supportsMultilingual": true, "supportsStructuredOutputs": false, "supportsRealTimeAPI": false, "liteLLM-provisioning": true, "inputCostPerToken": 5.5e-07, "outputCostPerToken": 2.1899999999999998e-06, "inputCostPer1kTokens": 0.00055, "outputCostPer1kTokens": 0.00219, "status": "GA", "key": "deepseek/deepseek-reasoner", "isAvailable": true, "modelGroup": "DeepSeek", "mode": "chat", "confidentiality": "public", "displayName": "DeepSeek-R1", "mappingInfo": {"mappingKey": "deepseek/deepseek-reasoner", "modelCardFile": "deepseek-r1.json", "modelCardId": "deepseek-r1-0528"}, "_isModelCard": true, "_modelCardSource": "DeepSeek API Documentation, vals.ai AIME Benchmark, Terminal-Bench Leaderboard, MATH 500 Benchmark Update", "_modelCardVersion": "1.3", "_enrichedWithMapping": true, "benchmarkData": {"model": "DeepSeek R1 (0528)", "modelid": "deepseek-r1-0528", "pass_rate_2": 71.4, "percent_cases_well_formed": 94.6, "total_cost": 4.8016, "command": "aider --model deepseek/deepseek-reasoner", "edit_format": "diff", "details": {"dirname": "2025-06-06-16-47-07--r1-diff", "test_cases": 224, "commit_hash": "4c161f9-dirty", "pass_rate_1": 34.4, "pass_num_1": 77, "pass_num_2": 160, "error_outputs": 28, "num_malformed_responses": 15, "num_with_malformed_responses": 12, "user_asks": 105, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 2, "total_tests": 225, "date": "2025-06-06", "versions": "0.84.1.dev", "seconds_per_case": 716.6, "prompt_tokens": 2644169, "completion_tokens": 1842168}}}, {"id": "deepseek/deepseek-v3", "name": "DeepSeek-V3", "provider": "DeepSeek", "description": "Ein starkes Mixture-of-Experts (MoE) Sprachmodell mit 671B Gesamtparametern und 37B aktivierten Parametern pro Token. Nutzt Multi-head Latent Attention (MLA) und DeepSeekMoE-Architekturen für effiziente Inferenz und kosteneffektives Training.", "contextWindow": 128000, "maxOutputTokens": 8192, "maxTokens": 8192, "maxInputTokens": 128000, "supportsFunctionCalling": true, "supportsVision": true, "supportsPdfInput": true, "supportsAudioInput": true, "supportsAudioOutput": false, "supportsSystemMessages": true, "supportsPromptCaching": false, "supportsReasoning": true, "supportsWebSearch": false, "supportsNativeStreaming": true, "supportsImageGeneration": false, "supportsCodeExecution": true, "supportsBatchProcessing": true, "supportsThinking": false, "supportsGrounding": false, "supportsMultilingual": true, "supportsStructuredOutputs": true, "supportsRealTimeAPI": false, "liteLLM-provisioning": false, "inputCostPerToken": 1.4e-07, "outputCostPerToken": 2.8e-07, "inputCostPer1kTokens": 0.00014000000000000001, "outputCostPer1kTokens": 0.00028000000000000003, "status": "GA", "key": "deepseek/deepseek-v3", "isAvailable": true, "modelGroup": "DeepSeek", "mode": "chat", "confidentiality": "public", "displayName": "DeepSeek-V3", "mappingInfo": {"mappingKey": "deepseek/deepseek-v3", "modelCardFile": "deepseek-v3.json", "modelCardId": "deepseek-v3@20241226"}, "_isModelCard": true, "_modelCardSource": "HuggingFace DeepSeek-V3 Model Card, DeepSeek-V3 Technical Report, Vals.ai AIME & MMMU Benchmarks, SWE-bench Leaderboard, Terminal-Bench Leaderboard, WebDev-Arena Leaderboard, LiveCodeBench Leaderboard, Vellum AI Leaderboard", "_modelCardVersion": "1.1", "_enrichedWithMapping": true, "benchmarkData": {"model": "DeepSeek V3 (0324)", "modelid": "deepseek-v3-0324", "pass_rate_2": 55.1, "percent_cases_well_formed": 99.6, "total_cost": 1.1164, "command": "aider --model deepseek/deepseek-chat", "edit_format": "diff", "details": {"dirname": "2025-03-24-15-41-33--deepseek-v3-0324-polyglot-diff", "test_cases": 225, "commit_hash": "502b863", "pass_rate_1": 28, "pass_num_1": 63, "pass_num_2": 124, "error_outputs": 32, "num_malformed_responses": 1, "num_with_malformed_responses": 1, "user_asks": 96, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 2, "test_timeouts": 4, "total_tests": 225, "date": "2025-03-24", "versions": "0.78.1.dev", "seconds_per_case": 290}}}, {"id": "xai/grok-3", "name": "Grok 3", "provider": "xAI", "description": "Das bisher leistungsstärkste Modell von xAI, das bei Unternehmensanwendungen wie Datenextraktion, Programmierung und Textzusammenfassung glänzt. Verfügt über tiefes Fachwissen in Finanzen, Gesundheitswesen, Recht und Wissenschaft.", "contextWindow": 131072, "maxOutputTokens": 32768, "maxTokens": 32768, "maxInputTokens": 131072, "supportsFunctionCalling": true, "supportsVision": true, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsSystemMessages": true, "supportsPromptCaching": true, "supportsReasoning": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsImageGeneration": false, "supportsCodeExecution": false, "supportsBatchProcessing": false, "supportsThinking": false, "supportsGrounding": false, "supportsMultilingual": true, "supportsStructuredOutputs": false, "supportsRealTimeAPI": false, "liteLLM-provisioning": false, "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "inputCostPer1kTokens": 0.003, "outputCostPer1kTokens": 0.015, "status": "Beta", "key": "xai/grok-3", "isAvailable": false, "modelGroup": "Grok", "mode": "chat", "confidentiality": "public", "displayName": "Grok 3", "mappingInfo": {"mappingKey": "xai/grok-3", "modelCardFile": "grok-v3.json", "modelCardId": "grok-3"}, "_isModelCard": true, "_modelCardSource": "xAI Documentation, vals.ai AIME Benchmark, vellum.ai LLM Leaderboard, LiveCodeBench Leaderboard, WebDev-Arena Leaderboard, Terminal-Bench Leaderboard, Aider Polyg<PERSON>", "_modelCardVersion": "1.4", "_enrichedWithMapping": true, "benchmarkData": {"model": "Grok 3 Beta", "modelid": "grok-3", "pass_rate_2": 53.3, "percent_cases_well_formed": 99.6, "total_cost": 11.0338, "command": "aider --model openrouter/x-ai/grok-3", "edit_format": "diff", "details": {"dirname": "2025-04-10-04-21-31--grok3-diff-exuser", "test_cases": 225, "commit_hash": "2dd40fc-dirty", "pass_rate_1": 22.2, "pass_num_1": 50, "pass_num_2": 120, "error_outputs": 1, "num_malformed_responses": 1, "num_with_malformed_responses": 1, "user_asks": 68, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 2, "total_tests": 225, "date": "2025-04-10", "versions": "0.81.2.dev", "seconds_per_case": 15.3}}}, {"id": "mistral/magistral-medium", "name": "Magistral Medium", "provider": "<PERSON><PERSON><PERSON>", "description": "Das erste Reasoning-<PERSON><PERSON> von Mistral AI mit überlegenen domänenspezifischen, transparenten und mehrsprachigen Reasoning-Fähigkeiten. Magistral Medium ist die leistungsstärkere Enterprise-Version.", "contextWindow": 128000, "maxOutputTokens": 40000, "maxTokens": 40000, "maxInputTokens": 128000, "supportsFunctionCalling": false, "supportsVision": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsSystemMessages": true, "supportsPromptCaching": false, "supportsReasoning": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsImageGeneration": false, "supportsCodeExecution": false, "supportsBatchProcessing": false, "supportsThinking": true, "supportsGrounding": false, "supportsMultilingual": true, "supportsStructuredOutputs": false, "supportsRealTimeAPI": false, "liteLLM-provisioning": true, "inputCostPerToken": 3e-06, "outputCostPerToken": 1.5e-05, "inputCostPer1kTokens": 0.003, "outputCostPer1kTokens": 0.015, "status": "Preview", "key": "mistral/magistral-medium", "isAvailable": true, "modelGroup": "Magistral", "mode": "chat", "confidentiality": "public", "displayName": "Magistral Medium", "mappingInfo": {"mappingKey": "mistral/magistral-medium", "modelCardFile": "magistral-medium.json", "modelCardId": "magistral-medium-2506"}, "_isModelCard": true, "_modelCardSource": "Mistral AI Blog Post, Ollama Documentation", "_modelCardVersion": "1.0", "_enrichedWithMapping": true}, {"id": "52de540253c915ae29abbe8fa51f4800a94f4a4bc44ea9c1445fec21c26359d8", "name": "dall-e-3", "key": "dall-e-3", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "image_generation", "inputCostPerToken": 0, "outputCostPerToken": 0, "contextWindow": null, "maxTokens": null, "maxInputTokens": null, "maxOutputTokens": null, "supportsFunctionCalling": false, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": false, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": false, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true}, {"id": "e14b8f46a4eef996a60b684b01d32ab58723daece0788871dcf046ecc2b2b192", "name": "gcp/codestral-2501", "key": "gcp/codestral-2501", "provider": "vertex_ai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "completion", "inputCostPerToken": 2e-07, "outputCostPerToken": 6e-07, "contextWindow": null, "maxTokens": null, "maxInputTokens": null, "maxOutputTokens": null, "supportsFunctionCalling": false, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": false, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": false, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true}, {"id": "bc323220bd2abd5c265f5a3a7ac229bd82b24a3c8a64c508dfd8e14a1f68d425", "name": "azure/text-embedding-ada-002", "key": "azure/text-embedding-ada-002", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "embedding", "inputCostPerToken": 1e-07, "outputCostPerToken": 0, "contextWindow": 8191, "maxTokens": 8191, "maxInputTokens": 8191, "maxOutputTokens": null, "supportsFunctionCalling": false, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": false, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": false, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true}, {"id": "5e909417b7d202166fb9e4dbe331fc5564b88b714a49f9ffba9d42c3c2a114b4", "name": "azure/o4-mini", "key": "azure/o4-mini", "provider": "azure", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "internal", "mode": "chat", "inputCostPerToken": 1.1e-06, "outputCostPerToken": 4.4e-06, "contextWindow": 200000, "maxTokens": 100000, "maxInputTokens": 200000, "maxOutputTokens": 100000, "supportsFunctionCalling": true, "supportsParallelFunctionCalling": false, "supportsResponseSchema": true, "supportsVision": true, "supportsPromptCaching": true, "supportsSystemMessages": false, "supportsToolChoice": true, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": true, "liteLLM-provisioning": true, "benchmarkData": {"model": "o4-mini (high)", "modelid": "o4-mini-high", "pass_rate_2": 72, "percent_cases_well_formed": 90.7, "total_cost": 19.6399, "command": "aider --model o4-mini", "edit_format": "diff", "details": {"dirname": "2025-04-16-22-01-58--o4-mini-high-diff-exsys", "test_cases": 225, "commit_hash": "b66901f-dirty", "pass_rate_1": 19.6, "pass_num_1": 44, "pass_num_2": 162, "error_outputs": 26, "num_malformed_responses": 24, "num_with_malformed_responses": 21, "user_asks": 66, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 2, "total_tests": 225, "date": "2025-04-16", "versions": "0.82.1.dev", "seconds_per_case": 176.5}}}, {"id": "3bc4cd707844c1be43142845e5b0c732006765cc8c661c6c529b7addd4b053eb", "name": "iteratec/bge-m3", "key": "iteratec/bge-m3", "provider": "openai", "description": "", "capabilities": [], "modelGroup": "Unknown", "isAvailable": true, "confidentiality": "confidential", "mode": "embedding", "inputCostPerToken": 0, "outputCostPerToken": 0, "contextWindow": 8192, "maxTokens": 8192, "maxInputTokens": 8192, "maxOutputTokens": null, "supportsFunctionCalling": false, "supportsParallelFunctionCalling": false, "supportsResponseSchema": false, "supportsVision": false, "supportsPromptCaching": false, "supportsSystemMessages": false, "supportsToolChoice": false, "supportsWebSearch": false, "supportsNativeStreaming": false, "supportsPdfInput": false, "supportsAudioInput": false, "supportsAudioOutput": false, "supportsAssistantPrefill": false, "supportsEmbeddingImageInput": false, "supportsReasoning": false, "liteLLM-provisioning": true}], "lastUpdated": "2025-06-21T08:40:19.136Z", "source": "api"}