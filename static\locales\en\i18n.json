{"site": {"title": "<PERSON><PERSON>", "description": "LLM Browser - Compare and evaluate language models"}, "nav": {"models": "Models", "blog": "Blog", "recommendations": "Recommendations", "benchmarks": "Benchmarks", "api_usage": "API Usage@iteratec"}, "footer": {"copyright": "© 2025 LLM Browser - Vise-Coding", "built_with": "Built with Astro, React, TypeScript & Tailwind CSS"}, "language_selector": {"label": "Select language", "de": "German", "en": "English", "pl": "Polish"}, "language": "Language", "redirect": "Redirecting to model overview...", "meta_description": "LLM Browser - Compare and evaluate language models", "vise_coding": "Vise-Coding", "loading": "Loading...", "models": {"header": "LLM Model Browser", "description": "Discover {{count}} AI models with detailed information on pricing, capabilities, and benchmark results. The goal is to make the selection of suitable models for everyday work more interactive, easier, and more transparent. Otherwise, the information must be gathered from many sources. Based on live data from iteraGPT (LiteLLM) and AI-generated model cards from various sources.", "filters": {"all": "All", "security": "Security", "mode": "Mode", "chat": "Cha<PERSON>", "completion": "Completion", "embedding": "Embedding", "image_generation": "Image Generation", "search_placeholder": "Search by name, provider or model group...", "of": "of", "models": "models"}, "stats": {"total_models": "Total Models", "benchmarks": "Benchmarks", "average_score": "Average Score", "top_performer": "Top Performer", "filtered_count": "{{filtered}} of {{total}} models"}, "comparison": {"title": "Model Comparison ({{count}} models)", "reset_selection": "Reset Selection", "property": "Property", "provider": "Provider", "litellm_availability": "LiteLLM Availability", "available": "Available", "model_card_only": "Model Card Only", "context_window": "Context Window", "max_output_tokens": "<PERSON> Output Tokens", "input_cost": "Input Cost (per 1M tokens)", "output_cost": "Output Cost (per 1M tokens)", "yes": "Yes", "no": "No", "capabilities": "Capabilities", "supported_platforms": "Supported Platforms", "metric": "Metric", "range": "Range", "no_details_available": "No detailed information available.", "other_benchmarks": "Other Benchmarks", "at": "at", "and": "and", "well_formed_code": "well formed code", "category": "Category", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "variants": "Variants", "not_available": "N/A", "aider_polyglot_short": "Aider-Polyglot (o)", "website": "Website", "paper": "Paper", "aider_benchmark": {"title": "Aider's polyglot benchmark", "description": "Measures the ability of models to edit and improve code in various programming languages.", "metric": "Pass Rate (2nd attempt)", "range": "0-100%", "fallback_description": "Aider's coding benchmark - measures the ability of models to edit and improve code."}}, "capabilities": {"vision": "Vision", "pdf_input": "PDF Input", "audio_input": "Audio Input", "audio_output": "Audio Output", "embedding_image": "Embedding Image", "function_calling": "Function Calling", "prompt_caching": "Prompt Caching", "reasoning": "Reasoning", "system_messages": "System Messages"}, "table": {"pagination": {"showing": "Showing {{start}} to {{end}} of {{total}} models", "previous": "Previous", "next": "Next"}, "headers": {"select": "Select", "security": "Security", "model_card": "Model Card", "litellm_status": "LiteLLM/Status", "name": "Name", "provider": "Provider", "mode": "Mode", "context": "Context", "max_output": "Max Output", "input_cost_per_million": "Input Cost/1M", "output_cost_per_million": "Output Cost/1M", "polyglot_score": "Polyglot Score", "support": "Support", "details": "Details", "show_filters": "Show Filters", "fullscreen": "Fullscreen"}, "tooltips": {"confidential": "Confidential", "internal": "Internal", "public": "Public", "model_card_available": "Model Card available", "deprecated": "Deprecated", "shutdown_date": "Shutdown: {{date}}", "litellm_available": "LiteLLM available", "model_card_only": "Model Card only", "chat": "Cha<PERSON>", "embedding": "Embedding", "image": "Image", "vision_processing": "Vision/Image processing", "pdf_input": "PDF Input", "audio_input": "Audio Input", "audio_output": "Audio Output", "embedding_image_input": "Embedding Image Input", "function_calling": "Function Calling", "prompt_caching": "Prompt Caching", "reasoning": "Reasoning", "system_messages": "System Messages"}, "empty_state": "No models found", "select_model": "Select model {{name}}", "details_button": "Details"}}, "blog": {"title": "LLM Blog", "description": "Latest insights on AI models, release notes, and benchmark analyses. Stay up to date with the latest developments in the LLM landscape.", "sectionModelAnalysis": "Model Analyses", "sectionModelAnalysisDesc": "Detailed reviews of the latest AI models", "sectionReleaseNotes": "Release Notes", "sectionReleaseNotesDesc": "Latest updates and changes to models", "sectionBenchmarkAnalysis": "Benchmark Analyses", "sectionBenchmarkAnalysisDesc": "In-depth evaluations of performance tests", "sectionIndustryNews": "Industry News", "sectionIndustryNewsDesc": "Important developments in the AI market"}, "recommendations": {"title": "Recommendations for enterprises", "description": "Intelligent recommendation system for {{totalModels}} LLM models from {{totalProviders}} providers. Based on standard use cases in enterprises, the optimal models for various scenarios are recommended. Benchmark performance, capabilities, costs, and other factors are considered for informed decisions.", "availableModels": "Available Models", "providers": "Providers", "gaStatus": "GA Status", "avgInputCost": "Avg. Input Cost", "avgOutputCost": "Avg. Output Cost", "perMillion": "per 1M tokens"}, "benchmark": {"title": "Benchmark Results", "description": "Detailed analysis of Polyglot benchmark results for {{totalBenchmarks}} benchmark tests.", "testedModels": "Tested Models", "averageScore": "Average Score", "highestScore": "Highest Score", "testCases": "Test Cases", "about": "About the Polyglot Benchmark:", "aboutText1": "This benchmark is based on Exercism coding exercises and tests the ability of language models to solve complex programming problems in 6 different languages:", "languages": "C++, Go, Java, JavaScript, Python, and Rust", "aboutText2": "The benchmark includes the {{hardest}} most difficult exercises out of a total of {{total}} available Exercism problems and was designed to be much more challenging than previous benchmarks. The scores are based on the number of successfully solved coding problems and provide a precise assessment of the code-editing capabilities of modern LLMs."}, "qc": {"title": "Benchmark Comparison for LLM Models", "header": "Benchmark Comparison", "description": "Detailed benchmark analysis for {{modelCount}} LLM models. Compare the performance of different models across {{benchmarkCount}} different benchmarks. This overview enables a direct cross-comparison of actual benchmark values from the model cards.", "availableBenchmarks": "Available Benchmarks", "avgBenchmarksPerModel": "Avg. Benchmarks/Model", "mostCommonBenchmark": "Most Common Benchmark", "modelsWithBenchmarks": "Models with Benchmarks", "topBenchmarks": "Top 5 Benchmarks (by availability)", "models": "Models"}, "components": {"collapsible_header": {"show_info": "Show info", "hide_info": "Hide info"}}}