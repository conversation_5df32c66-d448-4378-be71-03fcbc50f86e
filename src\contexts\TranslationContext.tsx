import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';

// Define the structure of our translations
export interface Translations {
  [key: string]: string | Translations;
}

// Define the context shape
interface TranslationContextType {
  translations: Translations;
  currentLang: string;
  changeLanguage: (lang: string) => Promise<void>;
}

// Create the context with default values
const TranslationContext = createContext<TranslationContextType>({
  translations: {},
  currentLang: 'de',
  changeLanguage: async () => {},
});

// Custom hook to use translations
export const useTranslation = () => useContext(TranslationContext);

// Helper function to get nested translation values
export const t = (translations: Translations, key: string, replacements?: Record<string, string | number>): string => {
  if (!translations) {
    return key;
  }
  
  try {
    // Handle empty key
    if (!key || key.trim() === '') {
      return '';
    }
    
    // Check if the key is directly in the translations object
    if (key in translations && typeof translations[key] === 'string') {
      return translations[key] as string;
    }
    
    const keys = key.split('.');
    let value: any = translations;
    
    // Navigate through nested objects
    for (let i = 0; i < keys.length; i++) {
      const k = keys[i];
      
      if (value === undefined || value === null || !(k in value)) {
        return key;
      }
      
      value = value[k];
    }
    
    // If value is not a string, return the key
    if (typeof value !== 'string') {
      return key;
    }
    
    // Handle replacements like {{count}}
    if (replacements) {
      return Object.entries(replacements).reduce((result, [placeholder, replacement]) => {
        return result.replace(new RegExp(`{{${placeholder}}}`, 'g'), String(replacement));
      }, value);
    }
    
    return value;
  } catch (error) {
    return key;
  }
};

interface TranslationProviderProps {
  children: ReactNode;
  initialTranslations?: Translations;
  initialLang?: string;
}

// Define global window interface
declare global {
  interface Window {
    __TRANSLATIONS__?: Translations;
    __CURRENT_LANG__?: string;
  }
}

export const TranslationProvider: React.FC<TranslationProviderProps> = ({
  children,
  initialTranslations = {},
  initialLang = 'de',
}) => {
  // Use global translations if available (from Astro's script tag)
  const globalTranslations = typeof window !== 'undefined' ? window.__TRANSLATIONS__ : undefined;
  const globalLang = typeof window !== 'undefined' ? window.__CURRENT_LANG__ : undefined;
  
  const [translations, setTranslations] = useState<Translations>(globalTranslations || initialTranslations);
  const [currentLang, setCurrentLang] = useState<string>(globalLang || initialLang);
  
  
  // Function to change language
  const changeLanguage = async (lang: string): Promise<void> => {
    try {
      // Validate language input
      if (!lang || !['de', 'en', 'pl'].includes(lang)) {
        return;
      }
      
      // Store preference immediately
      if (typeof window !== 'undefined') {
        localStorage.setItem('preferredLanguage', lang);
        document.cookie = `preferredLanguage=${lang}; path=/; max-age=${60 * 60 * 24 * 365}`;
      }
      
      // Reload the page to get server-side translations for the new language
      // This is more reliable than client-side fetching
      if (typeof window !== 'undefined') {
        window.location.reload();
      }
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };
  
  // Initialize translations on mount if not provided
  useEffect(() => {
    const loadInitialTranslations = async () => {
      try {
        // Check if we have global translations from server-side rendering
        if (globalTranslations && Object.keys(globalTranslations).length > 0) {
          setTranslations(globalTranslations);
          setCurrentLang(globalLang || initialLang);
          return;
        }
        
        // Only load if no initial translations were provided
        if (Object.keys(initialTranslations).length === 0) {
          try {
            // Get language from cookie, localStorage, or browser language
            let storedLang =
              (typeof document !== 'undefined' && document.cookie.split('; ').find(row => row.startsWith('preferredLanguage='))?.split('=')[1]) ||
              (typeof window !== 'undefined' && localStorage.getItem('preferredLanguage')) ||
              (typeof navigator !== 'undefined' && navigator.language?.split('-')[0]) ||
              initialLang;
            
            // Validate language
            if (!['de', 'en', 'pl'].includes(storedLang)) {
              console.warn(`Invalid stored language: ${storedLang}, falling back to ${initialLang}`);
              storedLang = initialLang;
            }
            
            console.log(`Loading translations for language: ${storedLang}`);
            
            // Fetch translations
            const response = await fetch(`/locales/${storedLang}/i18n.json`);
            if (!response.ok) {
              throw new Error(`Failed to fetch translations: ${response.status}`);
            }
            
            const text = await response.text();
            
            // Validate JSON before parsing
            if (!text || text.trim() === '') {
              throw new Error('Empty translation file');
            }
            
            try {
              // Basic JSON validation check
              if (!text.startsWith('{') || !text.endsWith('}')) {
                throw new Error('Invalid JSON format: not an object');
              }
              
              const newTranslations = JSON.parse(text);
              
              // Validate that we got an object
              if (typeof newTranslations !== 'object' || newTranslations === null) {
                throw new Error('Invalid translations format: not an object');
              }
              
              console.log(`Translations loaded successfully for ${storedLang}`);
              
              setTranslations(newTranslations);
              setCurrentLang(storedLang);
            } catch (jsonError) {
              console.error('Invalid JSON format:', jsonError);
              // Fall back to empty translations but don't crash
              setTranslations({});
            }
          } catch (error) {
            console.error('Failed to load translations:', error);
            // Fall back to empty translations but don't crash
            setTranslations({});
          }
        } else {
          // Use the provided initial translations
          setTranslations(initialTranslations);
          setCurrentLang(initialLang);
        }
      } catch (error) {
        console.error('Error in loadInitialTranslations:', error);
      }
    };
    
    loadInitialTranslations();
  }, [initialTranslations, initialLang]);
  
  const contextValue = { translations, currentLang, changeLanguage };
  
  return (
    <TranslationContext.Provider value={contextValue}>
      {children}
    </TranslationContext.Provider>
  );
};

export default TranslationContext;