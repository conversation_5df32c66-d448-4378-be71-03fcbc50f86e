{"basicInfo": {"modelId": "gemini-2.5-flash-preview-05-20", "displayName": "Gemini 2.5 Flash", "provider": "Google", "modelFamily": "Gemini", "version": "preview-05-20", "description": "Unser bestes Modell in Bezug auf Preis und Leistung und bietet umfassende Funktionen. Gemini 2.5 Flash ist unser erstes Flash-Modell mit Denkfunktionen", "releaseDate": "2025-05-20", "status": "Preview", "knowledgeCutoff": "Januar 2025"}, "technicalSpecs": {"contextWindow": 1048576, "maxOutputTokens": 65535, "supportedInputTypes": ["text", "image", "audio", "video", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"maxImages": 3000, "maxImageSize": "7 MB", "maxAudioLength": "8.4 Stunden", "maxVideoLength": "45 Minuten", "supportedMimeTypes": {"image": ["image/png", "image/jpeg", "image/webp"], "audio": ["audio/x-aac", "audio/flac", "audio/mp3", "audio/m4a", "audio/mpeg", "audio/mpga", "audio/mp4", "audio/opus", "audio/pcm", "audio/wav", "audio/webm"], "video": ["video/x-flv", "video/quicktime", "video/mpeg", "video/mpegs", "video/mpg", "video/mp4", "video/webm", "video/wmv", "video/3gpp"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": true, "audioOutput": false, "imageGeneration": false, "codeExecution": true, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": false, "thinking": true, "grounding": true, "multilingualSupport": true, "embeddingImageInput": true, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"tokensPerMinute": 1000000}, "temperature": {"min": 0, "max": 2, "default": 1.0}, "topP": 0.95, "topK": 64}, "pricing": {"inputCostPer1MTokens": 1.25, "outputCostPer1MTokens": 5.0, "cachingCosts": {"cacheWrites": 1.56, "cacheHits": 0.13}, "currency": "USD"}, "availability": {"regions": [{"region": "global", "availability": "Preview"}, {"region": "us-central1", "availability": "Preview"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI"]}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR", "ISO27001"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "GPQA Diamond", "category": "Science", "score": 78.3, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Graduate-level reasoning in science"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 44.2, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Software engineering benchmark for real-world coding tasks"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 16.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-17", "notes": "Terminus agent framework, ±1.3% confidence interval"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 55.1, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-25", "notes": "Aider polyglot benchmark with diff edit format (24k thinking tokens)"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 95.6, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-05-25", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1311.55, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #6"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 44.2, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 12.08, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Gemini 2.5 Flash (April 2025): 12.08±1.28"}], "metadata": {"lastUpdated": "2025-06-09T12:47:00Z", "dataSource": "Google Cloud Vertex AI Documentation, SWE-bench Verified Benchmark, Terminal-Bench Leaderboard", "version": "1.2", "variants": [{"modelId": "gemini-2.5-flash-preview-native-audio-dialog", "description": "Native Audio Dialog variant with audio input/output capabilities", "audioOutput": true, "maxOutputTokens": 128000, "supportedInputTypes": ["audio", "video"], "supportedOutputTypes": ["text", "audio"], "status": "Private Preview"}]}}