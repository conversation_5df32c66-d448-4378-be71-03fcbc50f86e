import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../../ui/card";
import type { EnrichedModelData } from "../types";

interface ModelTechnicalSpecsProps {
  model: EnrichedModelData;
}

export function ModelTechnicalSpecs({ model }: ModelTechnicalSpecsProps) {
  const modelCard = model.modelCard;

  // Helper function to format large numbers
  const formatNumber = (value: number | undefined | null) => {
    if (value == null) return "N/A";
    return value.toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Token Limits */}
      <Card>
        <CardHeader>
          <CardTitle>Token-Limits</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-muted-foreground">Kontext-Fenster</h3>
              <p className="text-lg">
                {formatNumber(modelCard?.technicalSpecs.contextWindow || model.contextWindow)} Tokens
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-muted-foreground">Max Output Tokens</h3>
              <p className="text-lg">
                {formatNumber(modelCard?.technicalSpecs.maxOutputTokens || model.maxOutputTokens || model.maxTokens)}
              </p>
            </div>
            {model.maxInputTokens && (
              <div>
                <h3 className="font-semibold text-muted-foreground">Max Input Tokens</h3>
                <p className="text-lg">{formatNumber(model.maxInputTokens)}</p>
              </div>
            )}
            {modelCard?.technicalSpecs && 'maxReasoningTokens' in modelCard.technicalSpecs && (
              <div>
                <h3 className="font-semibold text-muted-foreground">Max Reasoning Tokens</h3>
                <p className="text-lg">{formatNumber(modelCard.technicalSpecs.maxReasoningTokens ?? 0)}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Architecture & Parameters */}
      {modelCard?.technicalSpecs && (
        <Card>
          <CardHeader>
            <CardTitle>Architektur & Parameter</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              {modelCard.technicalSpecs.architecture && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Architektur</h3>
                  <p className="text-lg">{modelCard.technicalSpecs.architecture}</p>
                </div>
              )}
              {modelCard.technicalSpecs.parameterCount && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Parameter Count</h3>
                  <p className="text-lg">{modelCard.technicalSpecs.parameterCount}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Input/Output Types */}
      {modelCard?.technicalSpecs && (
        <Card>
          <CardHeader>
            <CardTitle>Unterstützte Modalitäten</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-muted-foreground mb-3">Input-Typen</h3>
                <div className="flex flex-wrap gap-2">
                  {modelCard.technicalSpecs.supportedInputTypes.map((type, _index) => (
                    <span key={`input-${type}`} className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 px-3 py-1 rounded-full text-sm">
                      {type}
                    </span>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-muted-foreground mb-3">Output-Typen</h3>
                <div className="flex flex-wrap gap-2">
                  {modelCard.technicalSpecs.supportedOutputTypes.map((type, _index) => (
                    <span key={`output-${type}`} className="bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400 px-3 py-1 rounded-full text-sm">
                      {type}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Input Limitations */}
      {modelCard?.technicalSpecs.inputLimitations && (
        <Card>
          <CardHeader>
            <CardTitle>Input-Limitierungen</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              {modelCard.technicalSpecs.inputLimitations.maxImages && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Max. Bilder</h3>
                  <p className="text-lg">{modelCard.technicalSpecs.inputLimitations.maxImages}</p>
                </div>
              )}
              {modelCard.technicalSpecs.inputLimitations.maxImageSize && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Max. Bildgröße</h3>
                  <p className="text-lg">{modelCard.technicalSpecs.inputLimitations.maxImageSize}</p>
                </div>
              )}
              {modelCard.technicalSpecs.inputLimitations.maxAudioLength && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Max. Audiolänge</h3>
                  <p className="text-lg">{modelCard.technicalSpecs.inputLimitations.maxAudioLength}</p>
                </div>
              )}
              {modelCard.technicalSpecs.inputLimitations.maxVideoLength && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Max. Videolänge</h3>
                  <p className="text-lg">{modelCard.technicalSpecs.inputLimitations.maxVideoLength}</p>
                </div>
              )}
            </div>

            {/* MIME Types */}
            {modelCard.technicalSpecs.inputLimitations.supportedMimeTypes && (
              <div className="mt-6">
                <h3 className="font-semibold text-muted-foreground mb-3">Unterstützte MIME-Typen</h3>
                <div className="space-y-3">
                  {Object.entries(modelCard.technicalSpecs.inputLimitations.supportedMimeTypes).map(([category, types]) => (
                    <div key={category}>
                      <h4 className="text-sm font-medium text-muted-foreground capitalize mb-1">{category}:</h4>
                      <div className="flex flex-wrap gap-1">
                        {types.map((type, _index) => (
                          <span key={`type-${type}`} className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded">
                            {type}
                          </span>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Legacy Data from API */}
      {!modelCard && (
        <Card>
          <CardHeader>
            <CardTitle>Legacy API Daten</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              {model.supportedModalities && (
                <div>
                  <h3 className="font-semibold text-muted-foreground mb-2">Modalitäten</h3>
                  <div className="flex flex-wrap gap-1">
                    {model.supportedModalities.map((modality, _index) => (
                      <span key={`modality-${modality}`} className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 px-2 py-1 rounded text-sm">
                        {modality}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              {model.supportedOutputModalities && (
                <div>
                  <h3 className="font-semibold text-muted-foreground mb-2">Output-Modalitäten</h3>
                  <div className="flex flex-wrap gap-1">
                    {model.supportedOutputModalities.map((modality, _index) => (
                      <span key={`output-modality-${modality}`} className="bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400 px-2 py-1 rounded text-sm">
                        {modality}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              {model.output_vector_size && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Vector Size</h3>
                  <p className="text-lg">{formatNumber(model.output_vector_size)}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}