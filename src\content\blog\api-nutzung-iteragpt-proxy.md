---
title: "API-Nutzung: iteraGPT Proxy für LLM-Integration"
excerpt: "Der iteraGPT API Proxy bietet eine OpenAI-kompatible Schnittstelle für alle im LLM Browser verfügbaren Modelle. Erfahren Sie, wie Sie die API konfigurieren und in Ihre Anwendungen integrieren."
category: "industry-news"
tags: ["api", "integration", "iteragpt", "proxy", "development", "openai-compatible"]
publishDate: "2025-06-11T17:30:00Z"
lastUpdated: "2025-06-11T17:30:00Z"
author:
  name: "LLM Browser Entwicklungsteam"
  role: "Product & Engineering"
readingTime: 6
changelog:
  - type: "added"
    description: "Initialversion"
    impact: "minor"
    technicalDetails: "Erstmal nur eine intiale Beschreibung aufgenommen"
featured: true
metaDescription: "Vollständige Anleitung zur Nutzung des iteraGPT API Proxy - OpenAI-kompatible Schnittstelle für alle LLM-Modelle mit Konfiguration, Authentifizierung und Beispielcode"
metaKeywords: ["iteraGPT API", "LLM API", "OpenAI kompatibel", "API Integration", "Proxy Configuration"]
featuredImage: "/images/blog/2025-06-api.png"
---

Der iteraGPT API Proxy stellt eine zentrale, OpenAI-kompatible Schnittstelle zur Verfügung, über die alle im LLM Browser aufgelisteten Modelle verwendet werden können. Diese einheitliche API vereinfacht die Integration verschiedener LLM-Anbieter erheblich.

## Grundkonfiguration

Für die Nutzung des iteraGPT API Proxy sind folgende Konfigurationsvariablen erforderlich:

| Variable | Wert | Beschreibung |
|----------|------|-------------|
| **Base URL** | `https://api.iteragpt.iteratec.de/v1` | API-Endpunkt des Proxy |
| **API Key** | benutzerdefiniert, `sk-****************` | Gültiger API-Key aus iteraGPT |
| **Modell** | benutzerdefiniert, üblicherweise `<provider>/<model>` | Name des zu verwendenden Modells, z.B. `azure/gpt-4o` (siehe Tabellen unten) |

### API-Key Authentifizierung

Für **alle** API-Requests muss der `Authorization`-Header entsprechend mit einem gültigen API-Key aus iteraGPT befüllt werden: `Bearer sk-****************`.

## Praktische Integration

### Python mit OpenAI SDK

Beispiel für die Nutzung eines API-Keys mit dem offiziellen Python `openai`-Paket:

```python
import openai

# Konfiguration des iteraGPT Proxy
client = openai.OpenAI(
    api_key="sk-****************",  # Ihr iteraGPT API-Key
    base_url="https://api.iteragpt.iteratec.de/v1"
)

# Beispiel-Request mit Claude Sonnet 4
response = client.chat.completions.create(
    model="gcp/claude-3-7-sonnet",
    messages=[
        {"role": "system", "content": "Sie sind ein hilfreicher Assistent."},
        {"role": "user", "content": "Erklären Sie die Vorteile von LLM-APIs."}
    ],
    max_tokens=1000,
    temperature=0.7
)

print(response.choices[0].message.content)
```

### JavaScript/TypeScript Integration

```typescript
import OpenAI from 'openai';

const client = new OpenAI({
  apiKey: 'sk-****************', // Ihr iteraGPT API-Key
  baseURL: 'https://api.iteragpt.iteratec.de/v1'
});

async function generateResponse() {
  const completion = await client.chat.completions.create({
    model: 'azure/gpt-4o',
    messages: [
      { role: 'system', content: 'Sie sind ein Experte für API-Integration.' },
      { role: 'user', content: 'Wie implementiere ich eine robuste LLM-API-Integration?' }
    ],
    max_tokens: 800,
    temperature: 0.5
  });

  return completion.choices[0].message.content;
}
```

## Weiterführende Ressourcen

- **[LLM Browser Modell-Übersicht](/models)**: Vollständige Liste aller verfügbaren Modelle
- **[Benchmark-Vergleiche](/benchmark)**: Performance-Daten für Modell-Auswahl insb. auf Basis der Aider Polyglot-Benchmark-Daten
- **[Empfehlungssystem](/empfehlungen)**: Use-Case-basierte Modell-Empfehlungen
- **[OpenAI API Dokumentation](https://platform.openai.com/docs/api-reference)**: Vollständige API-Referenz

Die iteraGPT API bietet eine einheitliche, zuverlässige Schnittstelle für die Integration verschiedenster LLM-Modelle in Ihre Anwendungen. Durch die OpenAI-Kompatibilität können bestehende Integrationen einfach auf die iteraGPT-Infrastruktur migriert werden.
