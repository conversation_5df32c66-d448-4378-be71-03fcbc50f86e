import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "../ui/dialog";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "../ui/tabs";
import type { EnrichedModelData } from "./types";
import { ModelRecommendations } from "../recommendations/ModelRecommendations";
import {
  ModelBasicInfo,
  ModelTechnicalSpecs,
  ModelCapabilities,
  ModelPerformance,
  ModelPricing,
  ModelBenchmarks,
  ModelAvailability
} from "./detail-sections";

interface ModelDetailDialogProps {
  model: EnrichedModelData | null;
  isOpen: boolean;
  onClose: () => void;
  formatCurrency: (value: number | null | undefined) => string;
}

export function ModelDetailDialog({ model, isOpen, onClose, formatCurrency }: ModelDetailDialogProps) {
  if (!model) return null;

  // Determine number of tabs based on available data
  const hasModelCard = !!model.modelCard;
  const hasRecommendations = hasModelCard;
  const hasAvailability = hasModelCard && model.modelCard?.availability;
  
  // Use fixed grid classes to avoid dynamic class issues
  const getGridClass = () => {
    const tabCount = 6 + (hasAvailability ? 1 : 0) + (hasRecommendations ? 1 : 0);
    switch (tabCount) {
      case 6: return "grid-cols-6";
      case 7: return "grid-cols-7";
      case 8: return "grid-cols-8";
      default: return "grid-cols-6";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl flex items-center gap-3">
            {model.name}
            <span className="text-sm bg-gray-100 px-2 py-1 rounded">
              {model.provider}
            </span>
            {model.modelCard?.basicInfo.status && (
              <span className={`text-xs px-2 py-1 rounded ${
                model.modelCard.basicInfo.status === 'GA' 
                  ? 'bg-green-100 text-green-800' 
                  : model.modelCard.basicInfo.status === 'Preview' 
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {model.modelCard.basicInfo.status}
              </span>
            )}
          </DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className={`grid w-full ${getGridClass()}`}>
            <TabsTrigger value="overview">Überblick</TabsTrigger>
            <TabsTrigger value="technical">Technik</TabsTrigger>
            <TabsTrigger value="capabilities">Fähigkeiten</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="pricing">Preise</TabsTrigger>
            <TabsTrigger value="benchmarks">Benchmarks</TabsTrigger>
            {hasAvailability && (
              <TabsTrigger value="availability">Verfügbarkeit</TabsTrigger>
            )}
            {hasRecommendations && (
              <TabsTrigger value="recommendations">Empfehlungen</TabsTrigger>
            )}
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            <ModelBasicInfo model={model} />
          </TabsContent>
          
          <TabsContent value="technical" className="space-y-4">
            <ModelTechnicalSpecs model={model} />
          </TabsContent>
          
          <TabsContent value="capabilities" className="space-y-4">
            <ModelCapabilities model={model} />
          </TabsContent>
          
          <TabsContent value="performance" className="space-y-4">
            <ModelPerformance model={model} />
          </TabsContent>
          
          <TabsContent value="pricing" className="space-y-4">
            <ModelPricing model={model} formatCurrency={formatCurrency} />
          </TabsContent>
          
          <TabsContent value="benchmarks" className="space-y-4">
            <ModelBenchmarks model={model} />
          </TabsContent>
          
          {hasAvailability && (
            <TabsContent value="availability" className="space-y-4">
              <ModelAvailability model={model} />
            </TabsContent>
          )}
          
          {hasRecommendations && (
            <TabsContent value="recommendations" className="space-y-4">
              <ModelRecommendations model={model} />
            </TabsContent>
          )}
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}