import * as React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "../../ui/card";
import type { EnrichedModelData } from "../types";

interface ModelBasicInfoProps {
  model: EnrichedModelData;
}

export function ModelBasicInfo({ model }: ModelBasicInfoProps) {
  const modelCard = model.modelCard;

  return (
    <div className="space-y-6">
      {/* Grundlegende Informationen */}
      <Card>
        <CardHeader>
          <CardTitle>Grundlegende Informationen</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-muted-foreground">Anbieter</h3>
              <p className="text-lg">{model.provider}</p>
            </div>
            <div>
              <h3 className="font-semibold text-muted-foreground">Modellgruppe</h3>
              <p className="text-lg">{modelCard?.basicInfo.modelFamily || model.modelGroup || "N/A"}</p>
            </div>
            <div>
              <h3 className="font-semibold text-muted-foreground">Verfügbarkeit</h3>
              <p className={`text-lg font-medium ${model.isAvailable ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"}`}>
                {model.isAvailable ? "✅ Verfügbar" : "❌ Nicht verfügbar"}
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-muted-foreground">Sicherheitsstufe</h3>
              <p className="text-lg capitalize">{model.confidentiality || "N/A"}</p>
            </div>
            {modelCard && (
              <>
                <div>
                  <h3 className="font-semibold text-muted-foreground">Version</h3>
                  <p className="text-lg">{modelCard.basicInfo.version}</p>
                </div>
                <div>
                  <h3 className="font-semibold text-muted-foreground">Status</h3>
                  <span className={`inline-block px-2 py-1 text-sm rounded ${
                    modelCard.basicInfo.status === 'GA'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : modelCard.basicInfo.status === 'Preview'
                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                      : 'bg-muted text-muted-foreground'
                  }`}>
                    {modelCard.basicInfo.status}
                  </span>
                </div>
                <div>
                  <h3 className="font-semibold text-muted-foreground">Release Date</h3>
                  <p className="text-lg">{modelCard.basicInfo.releaseDate}</p>
                </div>
                <div>
                  <h3 className="font-semibold text-muted-foreground">Knowledge Cutoff</h3>
                  <p className="text-lg">{modelCard.basicInfo.knowledgeCutoff}</p>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Beschreibung */}
      {modelCard?.basicInfo.description && (
        <Card>
          <CardHeader>
            <CardTitle>Beschreibung</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed">{modelCard.basicInfo.description}</p>
          </CardContent>
        </Card>
      )}

      {/* Model ID und interne Referenzen */}
      <Card>
        <CardHeader>
          <CardTitle>Technische Identifikation</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-muted-foreground">Model ID</h3>
              <p className="text-sm font-mono bg-muted px-2 py-1 rounded">{model.id}</p>
            </div>
            {modelCard?.basicInfo.modelId && (
              <div>
                <h3 className="font-semibold text-muted-foreground">Model Card ID</h3>
                <p className="text-sm font-mono bg-muted px-2 py-1 rounded">{modelCard.basicInfo.modelId}</p>
              </div>
            )}
            {model.key && (
              <div>
                <h3 className="font-semibold text-muted-foreground">Key</h3>
                <p className="text-sm font-mono bg-muted px-2 py-1 rounded">{model.key}</p>
              </div>
            )}
            {model.base_model && (
              <div>
                <h3 className="font-semibold text-muted-foreground">Base Model</h3>
                <p className="text-sm">{model.base_model}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}