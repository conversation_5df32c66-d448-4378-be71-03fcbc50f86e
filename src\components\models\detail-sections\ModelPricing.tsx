import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "../../ui/card";
import { DollarSign, TrendingDown, AlertCircle } from "lucide-react";
import type { EnrichedModelData } from "../types";

interface ModelPricingProps {
  model: EnrichedModelData;
  formatCurrency: (value: number | null | undefined) => string;
}

export function ModelPricing({ model, formatCurrency }: ModelPricingProps) {
  const modelCard = model.modelCard;

  // Helper function to format pricing consistently
  const formatPrice = (value: number | undefined | null, suffix: string = "") => {
    if (value == null) return "N/A";
    return `$${value.toFixed(2)}${suffix}`;
  };

  // Calculate cost comparison if both sources are available
  const hasModelCardPricing = modelCard?.pricing;
  const hasApiPricing = model.inputCostPerToken || model.outputCostPerToken;

  return (
    <div className="space-y-6">
      {/* Primary Pricing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            Standard-Preise
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-muted-foreground">Input Kosten (pro 1M Tokens)</h3>
              <p className="text-lg font-mono">
                {hasModelCardPricing
                  ? formatPrice(modelCard.pricing.inputCostPer1MTokens)
                  : formatCurrency(model.inputCostPerToken)
                }
              </p>
              {hasModelCardPricing && model.inputCostPerToken && (
                <p className="text-sm text-muted-foreground">
                  API: {formatCurrency(model.inputCostPerToken)}
                </p>
              )}
            </div>
            <div>
              <h3 className="font-semibold text-muted-foreground">Output Kosten (pro 1M Tokens)</h3>
              <p className="text-lg font-mono">
                {hasModelCardPricing
                  ? formatPrice(modelCard.pricing.outputCostPer1MTokens)
                  : formatCurrency(model.outputCostPerToken)
                }
              </p>
              {hasModelCardPricing && model.outputCostPerToken && (
                <p className="text-sm text-muted-foreground">
                  API: {formatCurrency(model.outputCostPerToken)}
                </p>
              )}
            </div>
            {hasModelCardPricing && (
              <div>
                <h3 className="font-semibold text-muted-foreground">Währung</h3>
                <p className="text-lg">{modelCard.pricing.currency}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Caching Costs */}
      {modelCard?.pricing.cachingCosts && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingDown className="w-5 h-5 text-green-600" />
              Caching-Kosten
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-muted-foreground">Cache Hits (pro 1M Tokens)</h3>
                <p className="text-lg font-mono text-green-600 dark:text-green-400">
                  {formatPrice(modelCard.pricing.cachingCosts.cacheHits)}
                </p>
                <p className="text-sm text-muted-foreground">
                  Deutlich günstiger für wiederholte Inhalte
                </p>
              </div>
              {modelCard.pricing.cachingCosts.cacheWrites && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Cache Writes (pro 1M Tokens)</h3>
                  <p className="text-lg font-mono">
                    {formatPrice(modelCard.pricing.cachingCosts.cacheWrites)}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Einmalige Kosten für Cache-Erstellung
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reasoning Costs (for o1/o3 models) */}
      {modelCard?.pricing?.reasoningCosts && (
        <Card>
          <CardHeader>
            <CardTitle>Reasoning-Kosten</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-muted-foreground">Reasoning Tokens (pro 1M)</h3>
                <p className="text-lg font-mono">
                  {formatPrice(modelCard.pricing.reasoningCosts.reasoningTokensPerMillion)}
                </p>
                <p className="text-sm text-muted-foreground">
                  Interne Denkprozesse
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-muted-foreground">Completion Tokens (pro 1M)</h3>
                <p className="text-lg font-mono">
                  {formatPrice(modelCard.pricing.reasoningCosts.completionTokensPerMillion)}
                </p>
                <p className="text-sm text-muted-foreground">
                  Finale Antwort-Tokens
                </p>
              </div>
            </div>
            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>Hinweis:</strong> Bei Reasoning-Modellen werden sowohl Reasoning- als auch Completion-Tokens berechnet.
                Die tatsächlichen Kosten können variieren je nach Komplexität der Anfrage.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Batch Processing Costs */}
      {modelCard?.pricing?.batchProcessingCosts && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingDown className="w-5 h-5 text-green-600" />
              Batch-Verarbeitung (50% Rabatt)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-muted-foreground">Input (pro 1M Tokens)</h3>
                <p className="text-lg font-mono text-green-600 dark:text-green-400">
                  {formatPrice(modelCard.pricing.batchProcessingCosts.inputCostPer1MTokens)}
                </p>
                <p className="text-sm text-muted-foreground">
                  Standard: {formatPrice(modelCard.pricing.inputCostPer1MTokens)}
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-muted-foreground">Output (pro 1M Tokens)</h3>
                <p className="text-lg font-mono text-green-600 dark:text-green-400">
                  {formatPrice(modelCard.pricing.batchProcessingCosts.outputCostPer1MTokens)}
                </p>
                <p className="text-sm text-muted-foreground">
                  Standard: {formatPrice(modelCard.pricing.outputCostPer1MTokens)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Legacy API Pricing */}
      {(model.inputCostPer1kTokens || model.outputCostPer1kTokens) && (
        <Card>
          <CardHeader>
            <CardTitle>Legacy API-Preise (pro 1K Tokens)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              {model.inputCostPer1kTokens && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Input (pro 1K Tokens)</h3>
                  <p className="text-lg font-mono">${model.inputCostPer1kTokens.toFixed(4)}</p>
                </div>
              )}
              {model.outputCostPer1kTokens && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Output (pro 1K Tokens)</h3>
                  <p className="text-lg font-mono">${model.outputCostPer1kTokens.toFixed(4)}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Alternative Pricing Models */}
      {(model.input_cost_per_character || model.input_cost_per_query || model.input_cost_per_second) && (
        <Card>
          <CardHeader>
            <CardTitle>Alternative Preismodelle</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              {model.input_cost_per_character && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Pro Zeichen (Input)</h3>
                  <p className="text-lg font-mono">${model.input_cost_per_character.toFixed(6)}</p>
                </div>
              )}
              {model.output_cost_per_character && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Pro Zeichen (Output)</h3>
                  <p className="text-lg font-mono">${model.output_cost_per_character.toFixed(6)}</p>
                </div>
              )}
              {model.input_cost_per_query && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Pro Anfrage</h3>
                  <p className="text-lg font-mono">${model.input_cost_per_query.toFixed(4)}</p>
                </div>
              )}
              {model.input_cost_per_second && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Pro Sekunde (Input)</h3>
                  <p className="text-lg font-mono">${model.input_cost_per_second.toFixed(4)}</p>
                </div>
              )}
              {model.output_cost_per_second && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Pro Sekunde (Output)</h3>
                  <p className="text-lg font-mono">${model.output_cost_per_second.toFixed(4)}</p>
                </div>
              )}
              {model.output_cost_per_image && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Pro Bild</h3>
                  <p className="text-lg font-mono">${model.output_cost_per_image.toFixed(4)}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Audio Pricing */}
      {(model.input_cost_per_audio_token || model.output_cost_per_audio_token) && (
        <Card>
          <CardHeader>
            <CardTitle>Audio-Preise</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              {model.input_cost_per_audio_token && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Audio Input (pro Token)</h3>
                  <p className="text-lg font-mono">${model.input_cost_per_audio_token.toFixed(6)}</p>
                </div>
              )}
              {model.output_cost_per_audio_token && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Audio Output (pro Token)</h3>
                  <p className="text-lg font-mono">${model.output_cost_per_audio_token.toFixed(6)}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Cache Pricing from API */}
      {(model.cache_creation_input_token_cost || model.cache_read_input_token_cost) && (
        <Card>
          <CardHeader>
            <CardTitle>Cache-Preise (API)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              {model.cache_creation_input_token_cost && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Cache Creation (pro Token)</h3>
                  <p className="text-lg font-mono">${model.cache_creation_input_token_cost.toFixed(6)}</p>
                </div>
              )}
              {model.cache_read_input_token_cost && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Cache Read (pro Token)</h3>
                  <p className="text-lg font-mono text-green-600 dark:text-green-400">${model.cache_read_input_token_cost.toFixed(6)}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pricing Notes */}
      {!hasModelCardPricing && !hasApiPricing && (
        <Card>
          <CardHeader>
            <CardTitle>Preis-Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 p-4 bg-muted rounded-lg">
              <AlertCircle className="w-5 h-5 text-muted-foreground" />
              <p className="text-muted-foreground">
                Keine Preisinformationen verfügbar. Bitte prüfen Sie die Anbieter-Dokumentation für aktuelle Preise.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}