---
import Layout from "../../../layouts/Layout.astro";
import BenchmarkTableIsland from "../../../components/benchmarks/aiderPolyglotBenchmark/BenchmarkTableIsland";
import { CollapsibleHeader } from "../../../components/models/CollapsibleHeader";
import type { ModelData, BenchmarkData } from "../../../types/api";
import * as fs from "node:fs/promises";
import * as path from "node:path";
import * as fsSync from "node:fs";

// Load translation data
const currentLang =
  Astro.cookies.get("preferredLanguage")?.value ||
  Astro.request.headers.get("accept-language")?.split(",")[0]?.split("-")[0] ||
  "de";

const translationPath = path.join(
  process.cwd(),
  "public",
  "locales",
  currentLang,
  "i18n.json"
);
const fallbackPath = path.join(
  process.cwd(),
  "public",
  "locales",
  "de",
  "i18n.json"
);

let translations: any = {};
try {
  if (fsSync.existsSync(translationPath)) {
    translations = JSON.parse(fsSync.readFileSync(translationPath, "utf-8"));
  } else {
    translations = JSON.parse(fsSync.readFileSync(fallbackPath, "utf-8"));
  }
} catch (error) {
  console.error("Error loading translations:", error);
  translations = JSON.parse(fsSync.readFileSync(fallbackPath, "utf-8"));
}

// Statische Daten zur Build-Zeit laden (GitLab Pages kompatibel)
// Verwende absoluten Pfad basierend auf dem Projekt-Root
const projectRoot = path.resolve(process.cwd());
const dataPath = path.join(projectRoot, "src", "data");

const modelsData = JSON.parse(
  await fs.readFile(path.join(dataPath, "models.json"), "utf-8")
);
const enrichedModelsData = JSON.parse(
  await fs.readFile(path.join(dataPath, "enriched-models.json"), "utf-8")
);
const benchmarksData = JSON.parse(
  await fs.readFile(path.join(dataPath, "polyglot_benchmarks.json"), "utf-8")
);
const statisticsData = JSON.parse(
  await fs.readFile(path.join(dataPath, "statistics.json"), "utf-8")
);

// Type assertions für die JSON-Daten - extrahiere models/benchmarks Array aus dem JSON
const models = (modelsData.models || modelsData) as ModelData[];
const enrichedModels = (enrichedModelsData.models ||
  enrichedModelsData) as ModelData[];
const benchmarks = (benchmarksData.benchmarks ||
  benchmarksData) as BenchmarkData[];
const statistics = {
  totalModels: statisticsData.models?.totalModels || 0,
  totalBenchmarks: statisticsData.benchmarks?.totalBenchmarks || 0,
  averagePassRate: statisticsData.benchmarks?.averagePassRate || 0,
  topPerformers:
    statisticsData.benchmarks?.topPerformers?.map((p: any) => p.model) || [],
};
---

<Layout
  title={`${translations.benchmark?.title || "Benchmark Ergebnisse"} - ${translations.site?.title || "LLM Browser"}`}
>
  <main class="container mx-auto px-4 py-8">
    <!-- Kollabierbare Header-Komponente -->
    <CollapsibleHeader
      title={translations.benchmark?.title || "Benchmark Ergebnisse"}
      description={translations.benchmark?.description?.replace(
        "{{totalBenchmarks}}",
        statistics.totalBenchmarks
      ) ||
        `Detaillierte Analyse der Polyglot-Benchmark-Ergebnisse für ${statistics.totalBenchmarks} Benchmark-Tests.`}
      client:only="react"
    >
      <!-- Benchmark-Statistik-Übersicht -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground">
            {translations.benchmark?.testedModels || "Getestete Modelle"}
          </h3>
          <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">
            {benchmarks.length}
          </p>
        </div>
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground">
            {translations.benchmark?.averageScore || "Durchschnittsscore"}
          </h3>
          <p class="text-3xl font-bold text-green-600 dark:text-green-400">
            {(statistics.averagePassRate || 0).toFixed(1)}%
          </p>
        </div>
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground">
            {translations.benchmark?.highestScore || "Höchster Score"}
          </h3>
          <p class="text-3xl font-bold text-purple-600 dark:text-purple-400">
            {
              benchmarks.length > 0
                ? Math.max(...benchmarks.map((b) => b.pass_rate_2)).toFixed(1)
                : "0"
            }%
          </p>
        </div>
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground">
            {translations.benchmark?.testCases || "Testfälle"}
          </h3>
          <p class="text-3xl font-bold text-orange-600 dark:text-orange-400">
            {
              benchmarks.length > 0
                ? benchmarks[0].details.test_cases || 225
                : 225
            }
          </p>
        </div>
      </div>

      <!-- Erklärungstext -->
      <div
        class="bg-blue-50 dark:bg-blue-900/30 border-l-4 border-blue-400 dark:border-blue-600 p-4 mb-6"
      >
        <div class="flex">
          <div class="ml-3">
            <p class="text-sm text-blue-700 dark:text-blue-300 mb-3">
              <strong
                >{
                  translations.benchmark?.about ||
                    "Über den Polyglot Benchmark:"
                }</strong
              >
              {
                translations.benchmark?.aboutText1 ||
                  "Dieser Benchmark basiert auf Exercism-Coding-Übungen und testet die Fähigkeit von Sprachmodellen, komplexe Programmierprobleme in 6 verschiedenen Sprachen zu lösen:"
              }
              <strong>
                {
                  translations.benchmark?.languages ||
                    "C++, Go, Java, JavaScript, Python und Rust"
                }</strong
              >.
            </p>
            <p class="text-sm text-blue-700 dark:text-blue-300 mb-3">
              {
                translations.benchmark?.aboutText2
                  ?.replace("{{hardest}}", "225")
                  .replace("{{total}}", "697") ||
                  "Der Benchmark umfasst die 225 schwierigsten Übungen aus insgesamt 697 verfügbaren Exercism-Problemen und wurde entwickelt, um deutlich herausfordernder zu sein als frühere Benchmarks. Die Scores basieren auf der Anzahl erfolgreich gelöster Coding-Probleme und bieten eine präzise Bewertung der Code-Editing-Fähigkeiten moderner LLMs."
              }
            </p>
            <p class="text-sm text-blue-700 dark:text-blue-300">
              <strong>Datenquelle:</strong> Die Benchmark-Daten stammen von
              <a
                href="https://aider.chat/docs/leaderboards/"
                target="_blank"
                rel="noopener noreferrer"
                class="text-blue-800 dark:text-blue-200 hover:text-blue-900 dark:hover:text-blue-100 underline font-medium"
              >
                aider.chat/docs/leaderboards/
              </a>
              und werden automatisch 1x täglich aktualisiert, um stets aktuelle Ergebnisse
              zu gewährleisten.
            </p>
          </div>
        </div>
      </div>
    </CollapsibleHeader>

    <!-- React Island für die interaktive Benchmark Table -->
    <BenchmarkTableIsland
      benchmarks={benchmarks}
      models={enrichedModels}
      client:only="react"
    />
  </main>
</Layout>

<style>
  .container {
    max-width: 1400px;
  }
</style>
