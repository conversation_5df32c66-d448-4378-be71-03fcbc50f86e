{"mappings": {"mappings": {"gcp/claude-3.7-sonnet": {"modelCardFile": "claude-sonnet-3.7.json", "modelCardId": "claude-3-7-sonnet@20250219"}, "anthropic/claude-3.7-sonnet": {"modelCardFile": "claude-sonnet-3.7.json", "modelCardId": "claude-3-7-sonnet@20250219"}, "aws/claude-3.7-sonnet": {"modelCardFile": "claude-sonnet-3.7.json", "modelCardId": "claude-3-7-sonnet@20250219"}, "gcp/claude-3.5-sonnet-v2": {"modelCardFile": "claude-3-5-sonnet-v2.json", "modelCardId": "claude-3-5-sonnet-v2@20241022"}, "anthropic/claude-3.5-sonnet": {"modelCardFile": "claude-3-5-sonnet-v2.json", "modelCardId": "claude-3-5-sonnet-v2@20241022"}, "gcp/claude-3.5-sonnet": {"modelCardFile": "claude-3-5-sonnet-v2.json", "modelCardId": "claude-3-5-sonnet-v2@20241022"}, "aws/claude-3.5-sonnet": {"modelCardFile": "claude-3-5-sonnet-v2.json", "modelCardId": "claude-3-5-sonnet-v2@20241022"}, "gcp/gemini-2.5-pro-preview-05-06": {"modelCardFile": "gemini-2.5-pro.json", "modelCardId": "gemini-2.5-pro-preview-05-06"}, "openai/gpt-4.1": {"modelCardFile": "gpt-4.1.json", "modelCardId": "gpt-4.1"}, "azure/gpt-4.1": {"modelCardFile": "gpt-4.1.json", "modelCardId": "gpt-4.1"}, "openai/gpt-4.1-mini": {"modelCardFile": "gpt-4.1-mini.json", "modelCardId": "gpt-4.1-mini-2025-04-14"}, "openai/gpt-4.1-nano": {"modelCardFile": "gpt-4.1-nano.json", "modelCardId": "gpt-4.1-nano-2025-04-14"}, "azure/gpt-4.1-nano": {"modelCardFile": "gpt-4.1-nano.json", "modelCardId": "gpt-4.1-nano-2025-04-14"}, "gpt-4.1-mini": {"modelCardFile": "gpt-4.1-mini.json", "modelCardId": "gpt-4.1-mini-2025-04-14"}, "GPT-4.1 mini": {"modelCardFile": "gpt-4.1-mini.json", "modelCardId": "gpt-4.1-mini-2025-04-14"}, "gpt-4.1-nano": {"modelCardFile": "gpt-4.1-nano.json", "modelCardId": "gpt-4.1-nano-2025-04-14"}, "GPT-4.1 nano": {"modelCardFile": "gpt-4.1-nano.json", "modelCardId": "gpt-4.1-nano-2025-04-14"}, "gcp/gemini-2.0-flash": {"modelCardFile": "gemini-2.0-flash.json", "modelCardId": "gemini-2.0-flash-001"}, "anthropic/claude-opus-4": {"modelCardFile": "claude-opus-4.json", "modelCardId": "claude-opus-4@20250514"}, "gcp/claude-opus-4": {"modelCardFile": "claude-opus-4.json", "modelCardId": "claude-opus-4@20250514"}, "anthropic/claude-sonnet-4": {"modelCardFile": "claude-sonnet-4.json", "modelCardId": "claude-sonnet-4@20250514"}, "gcp/claude-sonnet-4": {"modelCardFile": "claude-sonnet-4.json", "modelCardId": "claude-sonnet-4@20250514"}, "gcp/gemini-2.5-flash-preview-05-20": {"modelCardFile": "gemini-2.5-flash.json", "modelCardId": "gemini-2.5-flash-preview-05-20"}, "Gemini 2.5 Flash": {"modelCardFile": "gemini-2.5-flash.json", "modelCardId": "gemini-2.5-flash-preview-05-20"}, "Gemini 2.0 Flash": {"modelCardFile": "gemini-2.0-flash.json", "modelCardId": "gemini-2.0-flash-001"}, "Gemini 2.5 Pro": {"modelCardFile": "gemini-2.5-pro.json", "modelCardId": "gemini-2.5-pro-preview-05-06"}, "GPT-4.1": {"modelCardFile": "gpt-4.1.json", "modelCardId": "gpt-4.1"}, "openai/o3": {"modelCardFile": "o3.json", "modelCardId": "o3-2025-04-16"}, "azure/o3": {"modelCardFile": "o3.json", "modelCardId": "o3-2025-04-16"}, "o3": {"modelCardFile": "o3.json", "modelCardId": "o3-2025-04-16"}, "openai/o3-mini": {"modelCardFile": "o3-mini.json", "modelCardId": "o3-mini-2025-01-31"}, "openai/o3-pro": {"modelCardFile": "o3-pro.json", "modelCardId": "o3-pro-2025-06-10"}, "o3-pro": {"modelCardFile": "o3-pro.json", "modelCardId": "o3-pro-2025-06-10"}, "openai/gpt-4o": {"modelCardFile": "gpt-4o.json", "modelCardId": "gpt-4o-2024-08-06"}, "azure/gpt-4o": {"modelCardFile": "gpt-4o.json", "modelCardId": "gpt-4o-2024-08-06"}, "gpt-4o": {"modelCardFile": "gpt-4o.json", "modelCardId": "gpt-4o-2024-08-06"}, "GPT-4o": {"modelCardFile": "gpt-4o.json", "modelCardId": "gpt-4o-2024-08-06"}, "gcp/mistral-large-2411": {"modelCardFile": "mistral-large-2411.json", "modelCardId": "mistral-large-2411"}, "mistral-large-2411": {"modelCardFile": "mistral-large-2411.json", "modelCardId": "mistral-large-2411"}, "Mistral Large (24.11)": {"modelCardFile": "mistral-large-2411.json", "modelCardId": "mistral-large-2411"}, "openai/o4-mini": {"modelCardFile": "o4-mini.json", "modelCardId": "o4-mini-2025-04-16"}, "openai/gpt-4o-mini": {"modelCardFile": "gpt-4o-mini.json", "modelCardId": "gpt-4o-mini-2024-07-18"}, "azure/gpt-4o-mini": {"modelCardFile": "gpt-4o-mini.json", "modelCardId": "gpt-4o-mini-2024-07-18"}, "gpt-4o-mini": {"modelCardFile": "gpt-4o-mini.json", "modelCardId": "gpt-4o-mini-2024-07-18"}, "GPT-4o mini": {"modelCardFile": "gpt-4o-mini.json", "modelCardId": "gpt-4o-mini-2024-07-18"}, "iteratec/Qwen2.5-Coder-32B-Instruct": {"modelCardFile": "qwen2.5-coder-32b-instruct.json", "modelCardId": "qwen2.5-coder-32b-instruct"}, "Qwen2.5-Coder-32B-Instruct": {"modelCardFile": "qwen2.5-coder-32b-instruct.json", "modelCardId": "qwen2.5-coder-32b-instruct"}, "Qwen2.5 Coder 32B Instruct": {"modelCardFile": "qwen2.5-coder-32b-instruct.json", "modelCardId": "qwen2.5-coder-32b-instruct"}, "azure/DeepSeek-R1": {"modelCardFile": "deepseek-r1.json", "modelCardId": "deepseek-r1-0528"}, "deepseek/deepseek-reasoner": {"modelCardFile": "deepseek-r1.json", "modelCardId": "deepseek-r1-0528"}, "deepseek-reasoner": {"modelCardFile": "deepseek-r1.json", "modelCardId": "deepseek-r1-0528"}, "DeepSeek-R1": {"modelCardFile": "deepseek-r1.json", "modelCardId": "deepseek-r1-0528"}, "DeepSeek R1": {"modelCardFile": "deepseek-r1.json", "modelCardId": "deepseek-r1-0528"}, "deepseek/deepseek-v3": {"modelCardFile": "deepseek-v3.json", "modelCardId": "deepseek-v3@20241226"}, "deepseek-v3": {"modelCardFile": "deepseek-v3.json", "modelCardId": "deepseek-v3@20241226"}, "DeepSeek-V3": {"modelCardFile": "deepseek-v3.json", "modelCardId": "deepseek-v3@20241226"}, "DeepSeek V3": {"modelCardFile": "deepseek-v3.json", "modelCardId": "deepseek-v3@20241226"}, "xai/grok-3": {"modelCardFile": "grok-v3.json", "modelCardId": "grok-3"}, "grok-3": {"modelCardFile": "grok-v3.json", "modelCardId": "grok-3"}, "Grok 3 Beta": {"modelCardFile": "grok-v3.json", "modelCardId": "grok-3"}, "Grok 3": {"modelCardFile": "grok-v3.json", "modelCardId": "grok-3"}, "mistral/magistral-medium": {"modelCardFile": "magistral-medium.json", "modelCardId": "magistral-medium-2506"}, "magistral-medium": {"modelCardFile": "magistral-medium.json", "modelCardId": "magistral-medium-2506"}, "Magistral Medium": {"modelCardFile": "magistral-medium.json", "modelCardId": "magistral-medium-2506"}, "magistral-medium-2506": {"modelCardFile": "magistral-medium.json", "modelCardId": "magistral-medium-2506"}}}, "lastUpdated": "2025-06-21T08:40:19.137Z"}