---
import Layout from "../../layouts/Layout.astro";
import QualityCheckPage from "../../components/benchmarks/benchmarkTable";
import { CollapsibleHeader } from "../../components/models/CollapsibleHeader";
import type { ModelCardsData } from "../../types/model-cards";
import * as fs from "node:fs/promises";
import * as fsSync from "node:fs";
import * as path from "node:path";

// Statische Daten zur Build-Zeit laden (GitLab Pages kompatibel)
const projectRoot = path.resolve(process.cwd());
const dataPath = path.join(projectRoot, "src", "data");

const modelCardsData = JSON.parse(
  await fs.readFile(path.join(dataPath, "model-cards.json"), "utf-8")
) as ModelCardsData;

// Extrahiere nur die Model Cards
const modelCards = modelCardsData.modelCards;

// Berechne Statistiken für Benchmarks
const allBenchmarks = new Set<string>();
const benchmarkCounts = new Map<string, number>();

modelCards.forEach((card) => {
  if (card.benchmarks) {
    card.benchmarks.forEach((benchmark) => {
      allBenchmarks.add(benchmark.benchmarkName);
      benchmarkCounts.set(
        benchmark.benchmarkName,
        (benchmarkCounts.get(benchmark.benchmarkName) || 0) + 1
      );
    });
  }
});

const totalBenchmarks = allBenchmarks.size;
const avgBenchmarksPerModel =
  modelCards.reduce((sum, card) => sum + (card.benchmarks?.length || 0), 0) /
  modelCards.length;

// Finde die am häufigsten verwendeten Benchmarks
const topBenchmarks = Array.from(benchmarkCounts.entries())
  .sort((a, b) => b[1] - a[1])
  .slice(0, 5);

const cookieLang = Astro.cookies?.get("preferredLanguage")?.value;
const currentLang = cookieLang || "de";
const translationsPath = path.join(
  process.cwd(),
  "public",
  "locales",
  currentLang,
  "i18n.json"
);
const translations = JSON.parse(fsSync.readFileSync(translationsPath, "utf-8"));
---

<Layout title={translations.qc.title}>
  <main class="container mx-auto px-4 py-8">
    <!-- Kollabierbare Header-Komponente -->
    <CollapsibleHeader
      title={translations.qc.header}
      description={translations.qc.description
        .replace("{{modelCount}}", modelCards.length)
        .replace("{{benchmarkCount}}", totalBenchmarks)}
      client:only="react"
    >
      <!-- Benchmark-Statistiken -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-card rounded-lg border p-6">
          <h3 class="text-sm font-medium text-muted-foreground">
            {translations.qc?.availableBenchmarks || "Verfügbare Benchmarks"}
          </h3>
          <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">
            {totalBenchmarks}
          </p>
        </div>
        <div class="bg-card rounded-lg border p-6">
          <h3 class="text-sm font-medium text-muted-foreground">
            {translations.qc?.avgBenchmarksPerModel || "⌀ Benchmarks/Modell"}
          </h3>
          <p class="text-3xl font-bold text-green-600 dark:text-green-400">
            {avgBenchmarksPerModel.toFixed(1)}
          </p>
        </div>
        <div class="bg-card rounded-lg border p-6">
          <h3 class="text-sm font-medium text-muted-foreground">
            {translations.qc?.mostCommonBenchmark || "Häufigster Benchmark"}
          </h3>
          <p class="text-lg font-bold text-purple-600 dark:text-purple-400">
            {topBenchmarks[0]?.[0] || "N/A"}
          </p>
          <p class="text-xs text-muted-foreground">
            {topBenchmarks[0]?.[1] || 0}
            {translations.qc?.models || "Modelle"}
          </p>
        </div>
        <div class="bg-card rounded-lg border p-6">
          <h3 class="text-sm font-medium text-muted-foreground">
            {translations.qc?.modelsWithBenchmarks || "Modelle mit Benchmarks"}
          </h3>
          <p class="text-3xl font-bold text-orange-600 dark:text-orange-400">
            {
              modelCards.filter(
                (card) => card.benchmarks && card.benchmarks.length > 0
              ).length
            }
          </p>
        </div>
      </div>

      <!-- Top Benchmarks Liste -->
      <div class="bg-card rounded-lg border p-6 mb-6">
        <h3 class="text-lg font-semibold text-foreground mb-4">
          {
            translations.qc?.topBenchmarks ||
              "Top 5 Benchmarks (nach Verfügbarkeit)"
          }
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
          {
            topBenchmarks.map(([name, count]) => (
              <div class="text-center p-3 bg-muted rounded-lg">
                <p class="font-medium text-sm text-foreground">{name}</p>
                <p class="text-xs text-muted-foreground">
                  {count} {translations.qc?.models || "Modelle"}
                </p>
              </div>
            ))
          }
        </div>
      </div>
    </CollapsibleHeader>

    <!-- React Island für die Quality-Check-Seite -->
    <QualityCheckPage modelCards={modelCards} client:only="react" />
  </main>
</Layout>

<style>
  .container {
    max-width: 1400px;
  }
</style>
