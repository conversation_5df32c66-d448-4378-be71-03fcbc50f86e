---
import Layout from "@/layouts/Layout.astro";
import { BlogOverviewIsland } from "@/components/blog/BlogOverviewIsland.tsx";
import { getBlogPosts, getBlogMetadata } from "@/services/blog-server";
import type { BlogPost, BlogMetadata } from "@/types/blog";
import * as path from "node:path";
import * as fsSync from "node:fs";

// Load blog data using server-side services
let posts: BlogPost[] = [];
let metadata: BlogMetadata | null = null;

try {
  posts = await getBlogPosts();
  metadata = await getBlogMetadata();
} catch (error) {
  console.warn(
    "Could not load blog data:",
    error instanceof Error ? error.message : String(error)
  );
  posts = [];
  metadata = {
    totalPosts: 0,
    categories: {},
    tags: {},
    latestPosts: [],
    featuredPosts: [],
  };
}

const cookieLang = Astro.cookies?.get("preferredLanguage")?.value;
const currentLang = cookieLang || "de";
const translationsPath = path.join(
  process.cwd(),
  "public",
  "locales",
  currentLang,
  "i18n.json"
);
const translations = JSON.parse(fsSync.readFileSync(translationsPath, "utf-8"));

const pageTitle = translations["blog_title"];
const pageDescription = translations["blog_description"];
---

<Layout
  title={translations.blog.title}
  description={translations.blog.description}
>
  <main class="container mx-auto px-4 py-8">
    <!-- Page Header -->
    <div class="text-center mb-12">
      <h1 class="text-4xl font-bold text-gray-900 mb-4">
        {translations.blog.title}
      </h1>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        {translations.blog.description}
      </p>
    </div>

    <!-- Blog Overview with Interactive Components -->
    <BlogOverviewIsland
      client:load
      initialPosts={posts}
      initialMetadata={metadata}
      postsPerPage={9}
    />

    <!-- SEO and Meta Information -->
    <div class="mt-16 border-t pt-8">
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-center"
      >
        <div class="space-y-2">
          <h3 class="font-semibold text-gray-900">
            {translations.blog.sectionModelAnalysis}
          </h3>
          <p class="text-sm text-gray-600">
            {translations.blog.sectionModelAnalysisDesc}
          </p>
        </div>
        <div class="space-y-2">
          <h3 class="font-semibold text-gray-900">
            {translations.blog.sectionReleaseNotes}
          </h3>
          <p class="text-sm text-gray-600">
            {translations.blog.sectionReleaseNotesDesc}
          </p>
        </div>
        <div class="space-y-2">
          <h3 class="font-semibold text-gray-900">
            {translations.blog.sectionBenchmarkAnalysis}
          </h3>
          <p class="text-sm text-gray-600">
            {translations.blog.sectionBenchmarkAnalysisDesc}
          </p>
        </div>
        <div class="space-y-2">
          <h3 class="font-semibold text-gray-900">
            {translations.blog.sectionIndustryNews}
          </h3>
          <p class="text-sm text-gray-600">
            {translations.blog.sectionIndustryNewsDesc}
          </p>
        </div>
      </div>
    </div>
  </main>
</Layout>

<style>
  /* Blog-specific styles */
  .container {
    max-width: 1400px;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .container {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }
</style>
