import React, { useState, useEffect } from 'react';
import { Blog<PERSON><PERSON>, BlogCardList } from './BlogCard';
import { BlogFilters } from './BlogFilters';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { getBlogMetadata } from '@/services/blog';
import type { BlogPost, BlogFilters as BlogFiltersType, BlogSearchResult, BlogMetadata } from '@/types/blog';
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react';

interface BlogOverviewIslandProps {
  initialPosts?: BlogPost[];
  initialMetadata?: BlogMetadata;
  postsPerPage?: number;
}

export function BlogOverviewIsland({ 
  initialPosts = [], 
  initialMetadata,
  postsPerPage = 9 
}: BlogOverviewIslandProps) {
  const [posts, setPosts] = useState<BlogPost[]>(initialPosts);
  const [metadata, setMetadata] = useState<BlogMetadata | null>(initialMetadata || null);
  const [searchResult, setSearchResult] = useState<BlogSearchResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  
  // Filter state
  const [filters, setFilters] = useState<BlogFiltersType>({});
  const [searchQuery, setSearchQuery] = useState('');

  // Load initial metadata if not provided (only if no initialMetadata was passed)
  useEffect(() => {
    if (!initialMetadata && !metadata) {
      getBlogMetadata().then(setMetadata);
    }
  }, [initialMetadata, metadata]);

  // Search and filter effect - perform filtering locally using initialPosts
  useEffect(() => {
    const hasFiltersOrSearch = Object.keys(filters).length > 0 || searchQuery.length > 0;
    
    // If no filters or search, use initial posts directly
    if (!hasFiltersOrSearch) {
      // Apply pagination to initial posts
      const offset = (currentPage - 1) * postsPerPage;
      const paginatedPosts = (initialPosts || []).slice(offset, offset + postsPerPage);
      setPosts(paginatedPosts);
      setSearchResult(null); // Clear search results when no filters
      return;
    }

    // Only perform filtering when there are active filters or search
    const performLocalFilter = () => {
      setLoading(true);
      try {
        let filtered = initialPosts || [];
        
        // Apply category filter
        if (filters.category) {
          filtered = filtered.filter(post => post.category === filters.category);
        }
        
        // Apply tag filters
        if (filters.tags && filters.tags.length > 0) {
          filtered = filtered.filter(post =>
            filters.tags?.some(tag => post.tags.includes(tag)) ?? false
          );
        }
        
        // Apply featured filter
        if (filters.featured !== undefined) {
          filtered = filtered.filter(post => post.featured === filters.featured);
        }
        
        // Apply text search
        if (searchQuery) {
          const lowercaseQuery = searchQuery.toLowerCase();
          filtered = filtered.filter(post =>
            post.title.toLowerCase().includes(lowercaseQuery) ||
            post.excerpt.toLowerCase().includes(lowercaseQuery) ||
            post.content.toLowerCase().includes(lowercaseQuery) ||
            post.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
          );
        }
        
        // Calculate facets
        const categories: { [key: string]: number } = {};
        const tags: { [key: string]: number } = {};
        const models: { [key: string]: number } = {};
        
        filtered.forEach(post => {
          categories[post.category] = (categories[post.category] || 0) + 1;
          post.tags.forEach(tag => {
            tags[tag] = (tags[tag] || 0) + 1;
          });
          post.relatedModelIds?.forEach(modelId => {
            models[modelId] = (models[modelId] || 0) + 1;
          });
        });
        
        // Apply pagination
        const offset = (currentPage - 1) * postsPerPage;
        const paginatedPosts = filtered.slice(offset, offset + postsPerPage);
        
        const result = {
          posts: paginatedPosts,
          totalCount: filtered.length,
          facets: { categories, tags, models }
        };
        
        setSearchResult(result);
        setPosts(result.posts);
      } catch (error) {
        console.error('Error filtering posts:', error);
      } finally {
        setLoading(false);
      }
    };

    performLocalFilter();
  }, [filters, searchQuery, currentPage, postsPerPage, initialPosts]);

  const handleFiltersChange = (newFilters: BlogFiltersType) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when search changes
  };

  const handleClearFilters = () => {
    setFilters({});
    setSearchQuery('');
    setCurrentPage(1);
  };

  const totalPages = searchResult ? Math.ceil(searchResult.totalCount / postsPerPage) : 0;
  const hasResults = searchResult && searchResult.posts.length > 0;
  const hasFiltersOrSearch = Object.keys(filters).length > 0 || searchQuery.length > 0;

  return (
    <div className="space-y-8">
      {/* Header with Statistics */}
      {metadata && !hasFiltersOrSearch && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{metadata.totalPosts}</div>
              <div className="text-sm text-gray-600">Blog-Artikel</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{Object.keys(metadata.categories).length}</div>
              <div className="text-sm text-gray-600">Kategorien</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600">{metadata.featuredPosts.length}</div>
              <div className="text-sm text-gray-600">Featured Posts</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-orange-600">{Object.keys(metadata.tags).length}</div>
              <div className="text-sm text-gray-600">Tags</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Featured Posts Section (only show when no filters/search) */}
      {metadata && metadata.featuredPosts.length > 0 && !hasFiltersOrSearch && (
        <section className="space-y-4">
          <h2 className="text-xl font-semibold">Featured Artikel</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {metadata.featuredPosts.map((post) => (
              <BlogCard key={post.id} post={post} featured={true} />
            ))}
          </div>
        </section>
      )}

      {/* Main Content Area */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Filters Sidebar */}
        <div className="lg:col-span-1">
          <div className="sticky top-4">
            <BlogFilters
              filters={filters}
              searchQuery={searchQuery}
              searchResult={searchResult || undefined}
              initialMetadata={metadata || undefined}
              onFiltersChange={handleFiltersChange}
              onSearchChange={handleSearchChange}
              onClearFilters={handleClearFilters}
            />
          </div>
        </div>

        {/* Posts Content */}
        <div className="lg:col-span-3 space-y-6">
          {/* Results Header */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {loading ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Lade Artikel...</span>
                </div>
              ) : searchResult ? (
                `${searchResult.totalCount} Artikel${hasFiltersOrSearch ? ' gefunden' : ''}`
              ) : (
                `${posts.length} Artikel`
              )}
            </div>
            
            {hasFiltersOrSearch && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearFilters}
              >
                Alle Filter zurücksetzen
              </Button>
            )}
          </div>

          {/* Posts Grid */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: postsPerPage }, (_, i) => i).map((skeletonId) => (
                <Card key={`loading-skeleton-${skeletonId}`} className="h-80">
                  <CardContent className="p-4 animate-pulse">
                    <div className="bg-gray-200 h-4 rounded mb-2"></div>
                    <div className="bg-gray-200 h-3 rounded mb-2"></div>
                    <div className="bg-gray-200 h-3 rounded mb-4"></div>
                    <div className="bg-gray-200 h-20 rounded"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : hasResults ? (
            <BlogCardList posts={posts} />
          ) : (
            <Card className="p-8 text-center">
              <div className="text-gray-500">
                <div className="text-lg font-medium mb-2">Keine Artikel gefunden</div>
                <div className="text-sm">
                  {hasFiltersOrSearch 
                    ? 'Versuchen Sie andere Suchbegriffe oder Filter.' 
                    : 'Es sind noch keine Blog-Artikel verfügbar.'
                  }
                </div>
                {hasFiltersOrSearch && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearFilters}
                    className="mt-4"
                  >
                    Filter zurücksetzen
                  </Button>
                )}
              </div>
            </Card>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                disabled={currentPage === 1 || loading}
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                Vorherige
              </Button>
              
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  
                  return (
                    <Button
                      key={pageNum}
                      variant={currentPage === pageNum ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(pageNum)}
                      disabled={loading}
                      className="min-w-[2.5rem]"
                    >
                      {pageNum}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                disabled={currentPage === totalPages || loading}
              >
                Nächste
                <ChevronRight className="w-4 h-4 ml-1" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}