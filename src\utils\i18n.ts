import i18next from "i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import Backend from "i18next-http-backend";

// Helper to get cookie value
function getCookie(name: string): string | null {
  if (typeof document === "undefined") return null;
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(";").shift() || null;
  return null;
}

// Helper to set cookie
function setCookie(name: string, value: string, days = 365) {
  if (typeof document === "undefined") return;
  const expires = new Date(Date.now() + days * 864e5).toUTCString();
  document.cookie = `${name}=${value}; expires=${expires}; path=/`;
}

// Initialize i18next
i18next
  .use(Backend)
  .use(LanguageDetector)
  .init({
    fallbackLng: "de",
    debug: process.env.NODE_ENV === "development",
    interpolation: {
      escapeValue: false,
    },
    backend: {
      loadPath: "/locales/{{lng}}/{{ns}}.json",
    },
    detection: {
      order: ["cookie", "localStorage", "navigator"],
      caches: ["cookie", "localStorage"],
      lookupCookie: "preferredLanguage",
      lookupLocalStorage: "preferredLanguage",
    },
  });

// Get the current language from cookie, localStorage, or default to German
export const getCurrentLanguage = (): string => {
  if (typeof window !== "undefined") {
    return (
      getCookie("preferredLanguage") ||
      localStorage.getItem("preferredLanguage") ||
      "de"
    );
  }
  return "de";
};

// Change the language
export const changeLanguage = async (lang: string): Promise<void> => {
  await i18next.changeLanguage(lang);
  if (typeof window !== "undefined") {
    localStorage.setItem("preferredLanguage", lang);
    setCookie("preferredLanguage", lang);
  }
};

export default i18next;
